<template>
  <q-page class="q-pa-md">
    <!-- Loading overlay -->
    <div v-if="isLoading" class="full-page-loader">
      <q-spinner-dots color="primary" size="80px" />
      <div class="q-mt-md text-subtitle1">Loading dashboard...</div>
    </div>

    <!-- Main content - only show when not in loading state -->
    <div v-show="!isLoading">
      <div class="row q-col-gutter-md">
        <!-- Hero Section with Overflow Card -->
        <div class="col-12">
          <div class="dashboard-hero-container">
            <!-- Background Hero Image -->
            <div class="hero-background"></div>

            <!-- Overlapping Welcome Card -->
            <div class="hero-card-container">
              <div class="row justify-center">
                <div class="col-12 col-md-10 col-lg-8">
                  <q-card class="welcome-card-overflow">
                    <q-card-section class="q-pa-lg">
                      <!-- Welcome Message -->
                      <div class="text-h4 text-weight-light q-mb-sm text-center" style="color: #0D8A3E">
                        Welcome to Smile-Factory
                      </div>
                      <div class="text-subtitle1 q-mb-md text-center">
                        Welcome back, {{ authStore.currentUser?.email || '<EMAIL>' }}!
                      </div>
                      <div class="text-body1 q-mb-lg text-center" style="max-width: 600px; margin: 0 auto;">
                        <template v-if="!hasAnyProfileData">
                          Create your first profile to get started with Smile-Factory and
                          connect with like-minded innovators!
                        </template>
                        <template v-else-if="!isProfileComplete">
                          Complete your profile to get the most out of Smile-Factory and
                          connect with like-minded innovators!
                        </template>
                        <template v-else>
                          Welcome to your dashboard! Here you can manage your profiles and stay updated
                          with the latest events and opportunities.
                        </template>
                      </div>

                      <!-- Notification Alerts -->
                      <div v-if="unreadMessageCount > 0 || connectionRequestsCount > 0" class="notifications-section q-mb-md">
                        <div class="row justify-center q-gutter-sm">
                          <div v-if="unreadMessageCount > 0" class="col-auto">
                            <q-chip color="red" text-color="white" icon="chat" clickable @click="$router.push('/dashboard/messages')">
                              {{ unreadMessageCount }} message{{ unreadMessageCount > 1 ? 's' : '' }}
                            </q-chip>
                          </div>
                          <div v-if="connectionRequestsCount > 0" class="col-auto">
                            <q-chip color="purple" text-color="white" icon="person_add" clickable @click="$router.push('/dashboard/connections')">
                              {{ connectionRequestsCount }} request{{ connectionRequestsCount > 1 ? 's' : '' }}
                            </q-chip>
                          </div>
                        </div>
                      </div>

                      <!-- Action Buttons with AI Trigger -->
                      <div class="action-buttons text-center">
                        <div class="row justify-center q-gutter-sm">
                          <div v-if="!hasAnyProfileData" class="col-auto">
                            <q-btn
                              color="primary"
                              label="Create Profile"
                              :to="{ name: 'profile-create' }"
                              icon="person_add"
                              size="md"
                              class="cta-button primary-btn"
                              unelevated
                            />
                          </div>
                          <div v-else-if="!isProfileComplete" class="col-auto">
                            <q-btn
                              color="primary"
                              label="Complete Profile"
                              :to="{ name: 'profile-edit', params: { id: authStore.currentUser?.id } }"
                              icon="edit"
                              size="md"
                              class="cta-button primary-btn"
                              unelevated
                            />
                          </div>
                          <div v-else class="col-auto">
                            <q-btn
                              color="primary"
                              :label="getProfileSpecificCTA().label"
                              :to="getProfileSpecificCTA().route"
                              :icon="getProfileSpecificCTA().icon"
                              size="md"
                              class="cta-button primary-btn"
                              unelevated
                            />
                          </div>
                          <div class="col-auto">
                            <q-btn
                              color="green"
                              label="Explore Community"
                              to="/innovation-community"
                              icon="people"
                              size="md"
                              class="cta-button secondary-btn"
                              outline
                            />
                          </div>
                          <div class="col-auto">
                            <q-btn
                              color="purple"
                              label="AI Assistant"
                              icon="psychology"
                              @click="openAIChat"
                              size="md"
                              class="cta-button ai-btn"
                              unelevated
                            />
                          </div>
                        </div>
                      </div>
                    </q-card-section>
                  </q-card>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Profile Overview -->
        <div v-if="hasAnyProfileData" class="col-12">
          <q-card class="profile-overview-card">
            <q-card-section class="q-pa-lg">
              <div class="row items-center justify-between q-mb-md">
                <div class="text-h6 text-weight-medium">
                  <q-icon name="account_circle" class="q-mr-sm" />
                  Profile Overview
                </div>
                <div class="profile-actions">
                  <q-btn
                    icon="visibility"
                    label="View Profile"
                    :to="{ name: 'profile-dashboard' }"
                    size="sm"
                    class="profile-action-btn q-mr-sm"
                    flat
                  />
                  <q-btn
                    icon="edit"
                    label="Edit Profile"
                    :to="{ name: 'profile-edit', params: { id: authStore.currentUser?.id } }"
                    size="sm"
                    class="profile-action-btn"
                    flat
                  />
                </div>
              </div>

              <div class="row q-col-gutter-lg">
                <!-- Profile Info -->
                <div class="col-12 col-md-6">
                  <div class="profile-info-section">
                    <div class="row items-center q-gutter-sm q-mb-md">
                      <q-badge
                        :color="getProfileTypeColor(profileStore.currentProfile?.profile_type)"
                        :label="formatProfileType(profileStore.currentProfile?.profile_type)"
                        class="profile-type-badge"
                      />
                      <q-badge
                        :color="getProfileStateColor(profileStore.currentProfile?.profile_state)"
                        :label="profileStore.currentProfile?.profile_state || 'IN_PROGRESS'"
                        outline
                        class="profile-state-badge"
                      />
                    </div>
                    <div class="profile-details">
                      <div class="text-body2 text-grey-7">
                        {{ profileStore.currentProfile?.first_name }} {{ profileStore.currentProfile?.last_name }}
                      </div>
                      <div class="text-caption text-grey-6">
                        {{ profileStore.currentProfile?.bio?.substring(0, 100) }}{{ profileStore.currentProfile?.bio?.length > 100 ? '...' : '' }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Profile Completion with Linear Progress -->
                <div class="col-12 col-md-6">
                  <div class="completion-section">
                    <div class="completion-header q-mb-sm">
                      <div class="text-subtitle2 text-weight-medium">
                        Profile Completion
                        <span class="text-caption text-grey-6">({{ getCompletedSteps() }}/{{ getTotalSteps() }} steps)</span>
                      </div>
                      <div class="text-h6 text-weight-bold" :class="getCompletionColor(profileCompletionPercentage)">
                        {{ profileCompletionPercentage }}%
                      </div>
                    </div>

                    <q-linear-progress
                      :value="profileCompletionPercentage / 100"
                      size="8px"
                      :color="getCompletionColor(profileCompletionPercentage)"
                      track-color="grey-3"
                      class="q-mb-sm"
                      rounded
                    />

                    <div class="completion-status">
                      <div class="text-body2 text-weight-medium">
                        {{ getCompletionStatus(profileCompletionPercentage) }}
                      </div>
                      <div v-if="!isProfileComplete" class="text-caption text-grey-6">
                        Complete remaining steps to unlock all features
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Create Your First Profile - Only shown for users without profiles -->
        <div v-if="!hasAnyProfileData" class="col-12">
          <q-card class="q-pa-lg text-center">
            <q-card-section>
              <unified-icon name="person_add" size="64px" class="text-green-9 q-mb-md" />
              <div class="text-h5 text-green-9">Create Your First Profile</div>
              <p class="text-body1 q-my-md">To get started, create your first profile by selecting your role in the ecosystem.</p>
              <q-btn
                color="green"
                label="Create Your Profile"
                :to="{ name: 'profile-create' }"
                size="md"
                class="cta-button primary-btn q-mt-md"
                unelevated
              >
                <template v-slot:prepend>
                  <unified-icon name="person_add" class="q-mr-xs" />
                </template>
              </q-btn>
            </q-card-section>
          </q-card>
        </div>

        <!-- Type-specific Dashboard - Only shown for users with profiles -->
        <div v-if="hasAnyProfileData" class="col-12">
          <type-specific-dashboard :profile-type="profileStore.currentProfile?.profile_type" />
        </div>

        <!-- User Content Management removed as requested -->

        <!-- Minimal Smart Tools -->
        <div class="col-12">
          <q-card class="minimal-tools-card">
            <q-card-section class="q-pa-md">
              <div class="row items-center justify-between q-mb-sm">
                <div class="text-subtitle1 text-weight-medium">
                  <q-icon name="auto_awesome" class="q-mr-xs" />
                  Smart Suggestions
                </div>
                <q-btn flat dense color="grey-7" icon="more_horiz" size="sm">
                  <q-menu>
                    <q-list style="min-width: 200px">
                      <q-item clickable v-close-popup @click="openAIChat">
                        <q-item-section avatar>
                          <q-icon name="psychology" />
                        </q-item-section>
                        <q-item-section>AI Assistant</q-item-section>
                      </q-item>
                      <q-item clickable v-close-popup to="/innovation-community">
                        <q-item-section avatar>
                          <q-icon name="explore" />
                        </q-item-section>
                        <q-item-section>Explore Community</q-item-section>
                      </q-item>
                      <q-item clickable v-close-popup to="/dashboard/help">
                        <q-item-section avatar>
                          <q-icon name="help" />
                        </q-item-section>
                        <q-item-section>Help Center</q-item-section>
                      </q-item>
                    </q-list>
                  </q-menu>
                </q-btn>
              </div>

              <!-- Profile-Specific Smart Suggestions -->
              <div class="suggestions-row">
                <div class="row q-gutter-xs">
                  <div
                    v-for="suggestion in getSmartSuggestions()"
                    :key="suggestion.id"
                    class="col-auto"
                  >
                    <q-chip
                      :color="suggestion.color"
                      text-color="white"
                      :icon="suggestion.icon"
                      clickable
                      :to="suggestion.route"
                      @click="suggestion.action ? handleAITrigger(suggestion.action) : null"
                      size="sm"
                      class="smart-suggestion-chip"
                    >
                      {{ suggestion.label }}
                    </q-chip>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Dashboard Grid Layout -->
        <div class="col-12 col-md-6">
          <q-card class="dashboard-widget activity-widget">
            <q-card-section class="widget-header">
              <div class="row items-center justify-between">
                <div class="widget-title">
                  <q-icon name="timeline" color="blue" class="q-mr-sm" />
                  <span>Recent Activity</span>
                  <notification-badge
                    v-if="unreadActivitiesCount > 0"
                    :count="unreadActivitiesCount"
                    color="blue"
                    rounded
                    class="q-ml-sm"
                  />
                </div>
                <q-btn
                  flat
                  dense
                  color="blue"
                  icon="arrow_forward"
                  to="/dashboard/activity"
                  size="sm"
                />
              </div>
            </q-card-section>
            <q-separator />
            <q-card-section class="widget-content">
              <activity-feed :limit="3" title="" compact />
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-6">
          <q-card class="dashboard-widget connections-widget">
            <q-card-section class="widget-header">
              <div class="row items-center justify-between">
                <div class="widget-title">
                  <q-icon name="people" color="purple" class="q-mr-sm" />
                  <span>Connections</span>
                  <notification-badge
                    v-if="connectionRequestsCount > 0"
                    :count="connectionRequestsCount"
                    color="red"
                    rounded
                    class="q-ml-sm"
                  />
                </div>
                <q-btn
                  flat
                  dense
                  color="purple"
                  icon="arrow_forward"
                  to="/dashboard/connections"
                  size="sm"
                />
              </div>
            </q-card-section>
            <q-separator />
            <q-card-section class="widget-content">
              <connection-requests :limit="3" title="" compact />
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-6">
          <q-card class="dashboard-widget bookmarks-widget">
            <q-card-section class="widget-header">
              <div class="row items-center justify-between">
                <div class="widget-title">
                  <q-icon name="bookmark" color="cyan" class="q-mr-sm" />
                  <span>Saved Content</span>
                  <q-badge v-if="totalSavedItems > 0" color="cyan" class="q-ml-sm">
                    {{ totalSavedItems }}
                  </q-badge>
                </div>
                <q-btn
                  flat
                  dense
                  color="cyan"
                  icon="arrow_forward"
                  to="/dashboard/activity"
                  size="sm"
                />
              </div>
            </q-card-section>
            <q-separator />
            <q-card-section class="widget-content">
              <div v-if="loadingBookmarks" class="text-center q-pa-md">
                <q-spinner color="cyan" size="2em" />
                <div class="text-caption q-mt-sm">Loading...</div>
              </div>
              <div v-else-if="recentBookmarks.length === 0" class="empty-state">
                <q-icon name="bookmark_border" size="3em" color="grey-4" />
                <div class="text-subtitle2 q-mt-sm">No bookmarks yet</div>
                <div class="text-caption text-grey-6">Save posts and content to see them here</div>
              </div>
              <q-list v-else>
                <q-item
                  v-for="bookmark in recentBookmarks.slice(0, 3)"
                  :key="bookmark.id"
                  clickable
                  @click="viewBookmarkedContent(bookmark)"
                  class="bookmark-item"
                >
                  <q-item-section avatar>
                    <q-avatar color="cyan" text-color="white" size="sm">
                      <unified-icon :name="getBookmarkIcon(bookmark)" />
                    </q-avatar>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label lines="1" class="text-weight-medium">{{ getBookmarkTitle(bookmark) }}</q-item-label>
                    <q-item-label caption>{{ getBookmarkType(bookmark) }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-6">
          <q-card class="dashboard-widget events-widget">
            <q-card-section class="widget-header">
              <div class="row items-center justify-between">
                <div class="widget-title">
                  <q-icon name="event" color="green" class="q-mr-sm" />
                  <span>Upcoming Events</span>
                </div>
                <q-btn
                  flat
                  dense
                  color="green"
                  icon="arrow_forward"
                  to="/dashboard/events"
                  size="sm"
                />
              </div>
            </q-card-section>
            <q-separator />
            <q-card-section class="widget-content">
              <q-list>
                <q-item v-for="i in 3" :key="i" clickable to="/dashboard/events" class="event-item">
                  <q-item-section avatar>
                    <q-avatar color="green" text-color="white" size="sm">
                      <unified-icon name="event" />
                    </q-avatar>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="text-weight-medium">Innovation Workshop {{ i }}</q-item-label>
                    <q-item-label caption>
                      <q-icon name="schedule" size="xs" class="q-mr-xs" />
                      July {{ 15 + i }}, 2025
                    </q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-chip color="green" text-color="white" size="sm" dense>
                      {{ i === 1 ? 'Tomorrow' : `${i} days` }}
                    </q-chip>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-6">
          <q-card class="dashboard-widget announcements-widget">
            <q-card-section class="widget-header">
              <div class="row items-center justify-between">
                <div class="widget-title">
                  <q-icon name="campaign" color="orange" class="q-mr-sm" />
                  <span>Announcements</span>
                </div>
                <q-btn
                  flat
                  dense
                  color="orange"
                  icon="arrow_forward"
                  to="/dashboard/announcements"
                  size="sm"
                />
              </div>
            </q-card-section>
            <q-separator />
            <q-card-section class="widget-content">
              <q-list>
                <q-item v-for="i in 3" :key="i" clickable to="/dashboard/announcements" class="announcement-item">
                  <q-item-section avatar>
                    <q-avatar color="orange" text-color="white" size="sm">
                      <unified-icon name="campaign" />
                    </q-avatar>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="text-weight-medium">Platform Update {{ i }}</q-item-label>
                    <q-item-label caption>
                      <q-icon name="schedule" size="xs" class="q-mr-xs" />
                      August {{ 10 + i }}, 2025
                    </q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-chip color="orange" text-color="white" size="sm" dense>
                      New
                    </q-chip>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-6">
          <q-card class="dashboard-widget messages-widget" :class="{'widget-highlight': unreadMessageCount > 0}">
            <q-card-section class="widget-header" :class="unreadMessageCount > 0 ? 'bg-red-1' : ''">
              <div class="row items-center justify-between">
                <div class="widget-title">
                  <q-icon name="chat" :color="unreadMessageCount > 0 ? 'red' : 'blue'" class="q-mr-sm" />
                  <span>Messages</span>
                  <q-badge v-if="unreadMessageCount > 0" color="red" class="q-ml-sm">
                    {{ unreadMessageCount }}
                  </q-badge>
                </div>
                <q-btn
                  flat
                  dense
                  :color="unreadMessageCount > 0 ? 'red' : 'blue'"
                  icon="arrow_forward"
                  to="/dashboard/messages"
                  size="sm"
                />
              </div>
            </q-card-section>
            <q-separator />
            <q-card-section class="widget-content">
              <div v-if="loadingConversations" class="text-center q-pa-md">
                <q-spinner color="blue" size="2em" />
                <div class="text-caption q-mt-sm">Loading...</div>
              </div>
              <div v-else-if="conversations.length === 0" class="empty-state">
                <q-icon name="chat" size="3em" color="grey-4" />
                <div class="text-subtitle2 q-mt-sm">No messages yet</div>
                <div class="text-caption text-grey-6">Start a conversation to see messages here</div>
              </div>
              <q-list v-else>
                <q-item
                  v-for="conversation in conversations.slice(0, 3)"
                  :key="conversation.id"
                  clickable
                  to="/dashboard/messages"
                  class="message-item"
                  :class="{'message-unread': !conversation.is_read && conversation.recipient_id === currentUserId}"
                >
                  <q-item-section avatar>
                    <user-avatar
                      :user-id="conversation.other_user_id"
                      :name="getUserName(conversation.other_user)"
                      :email="conversation.other_user?.email || ''"
                      :avatar-url="conversation.other_user?.avatar_url"
                      size="32px"
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="text-weight-medium">
                      {{ getUserName(conversation.other_user) }}
                    </q-item-label>
                    <q-item-label caption lines="1" class="ellipsis">
                      {{ conversation.content }}
                    </q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <div class="text-caption text-grey-6">
                      {{ formatDate(conversation.created_at) }}
                    </div>
                    <q-badge v-if="!conversation.is_read && conversation.recipient_id === currentUserId"
                      color="red" floating />
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>




      </div>
    </div>

    <!-- Profile completion popup -->
    <q-dialog v-model="showProfileCompletionPopup" persistent>
      <profile-completion-popup
        v-model="showProfileCompletionPopup"
        :is-initial="isNewUser"
        @remind-later="handleRemindLater"
      />
    </q-dialog>

    <!-- Profile creation modal -->
    <profile-creation-modal
      v-model="showProfileCreationModal"
      @profile-created="handleProfileCreated"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { date } from 'quasar'
import { useAuthStore } from '../../stores/auth'
import { useProfileStore } from '../../stores/profile'
import { useNotificationStore } from '../../stores/notifications'
import { useActivityNotificationsStore } from '../../stores/activityNotifications'
import { supabase } from '../../lib/supabase'
import { useConnectionsStore } from '../../stores/connections'
import { serviceCoordinator } from '../../services/ServiceCoordinator'
import UnifiedIcon from '../../components/ui/UnifiedIcon.vue'
import NotificationBadge from '../../components/common/NotificationBadge.vue'
import ProfileCompletionPopup from '../../components/profile/ProfileCompletionPopup.vue'
import ProfileCreationModal from '../../components/profile/ProfileCreationModal.vue'
import ProfileCompletionStatus from '../../components/profile/ProfileCompletionStatus.vue'
import TypeSpecificDashboard from '../../components/dashboard/TypeSpecificDashboard.vue'

import ActivityFeed from '../../components/activity/ActivityFeed.vue'
import ConnectionsList from '../../components/connections/ConnectionsList.vue'
import ConnectionRequests from '../../components/connections/ConnectionRequests.vue'
import UserAvatar from '../../components/common/UserAvatar.vue'
import AIFeaturesCard from '../../components/ai/AIFeaturesCard.vue'
import ProfileAwareAITriggers from '../../components/ai/ProfileAwareAITriggers.vue'
import { formatProfileType, getProfileTypeIcon } from '../../services/profileTypes'
import { useMessagingStore, type Conversation } from '../../stores/messaging'
import { getUniversalUsername } from '../../utils/userUtils'
import { useUserInteractionsStore } from '../../stores/userInteractions'
import { useGlobalServicesStore } from '../../stores/globalServices'

// Import simplified services
import { useUserState } from '../../services/userStateService'
import { useProfileCompletion } from '../../services/profileCompletionService'

const authStore = useAuthStore()
const profileStore = useProfileStore()
const notifications = useNotificationStore()
const globalServices = useGlobalServicesStore()
const router = useRouter()

// Use simplified services
const { isNewUser, hasIncompleteProfile, hasAnyProfileData, isLoading: userStateLoading, checkUserState, profileData } = useUserState()
const { isProfileComplete } = useProfileCompletion()

// Simple loading state
const isLoading = ref(true)

// UI state
const showProfileCompletionPopup = ref(false)
const showProfileCreationModal = ref(false)
const communityButtonLoading = ref(false)
const aiTriggerLoading = ref(false)

// Messaging and activity state
const messagingStore = useMessagingStore()
const activityNotificationsStore = useActivityNotificationsStore()
const connectionsStore = useConnectionsStore()
const userInteractionsStore = useUserInteractionsStore()
const conversations = ref<Conversation[]>([])
const loadingConversations = ref(false)
const loadingBookmarks = ref(false)
const currentUserId = computed(() => authStore.currentUser?.id || '')

// Notification counts
const unreadMessageCount = computed(() => messagingStore.unreadCount)
const connectionRequestsCount = computed(() => activityNotificationsStore.connectionRequests)
const unreadActivitiesCount = computed(() => activityNotificationsStore.unreadActivities)
const totalActivityCount = computed(() => activityNotificationsStore.totalUnreadCount)

// Bookmarks data
const totalSavedItems = computed(() => userInteractionsStore.totalSavedItems)
const recentBookmarks = computed(() => {
  // Combine saved posts and profiles, sort by date, take first 3
  const allBookmarks = [
    ...userInteractionsStore.savedPosts.map(item => ({
      ...item,
      type: 'post',
      title: item.post?.title || 'Untitled Post',
      content_type: item.post?.post_type || 'post'
    })),
    ...userInteractionsStore.savedProfiles.map(item => ({
      ...item,
      type: 'profile',
      title: item.profile?.name || 'Profile',
      content_type: 'profile'
    }))
  ]

  return allBookmarks
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 3)
})

// Lifecycle hooks
onMounted(async () => {
  isLoading.value = true
  try {
    // Direct check of user state - single database query
    await checkUserState()

    // Load minimal profile data if needed
    if (hasAnyProfileData.value && !profileStore.currentProfile) {
      await profileStore.loadUserProfiles()
    }

    // Services are coordinated by DashboardLayout via ServiceCoordinator
    // This prevents duplicate API calls and improves performance
    console.log('Dashboard: Using ServiceCoordinator for service management')
    console.log('Service initialization status:', serviceCoordinator.getStats())

    // Wait for critical services to be ready
    const servicesReady = await serviceCoordinator.waitForAllServices(10000) // 10 second timeout
    if (servicesReady) {
      console.log('✅ Dashboard: All services ready')

      // Synchronize connection data to ensure accurate counts
      await connectionsStore.synchronizeConnectionData()

      console.log('Unread message count:', messagingStore.unreadCount)
      console.log('Connection requests:', connectionRequestsCount.value)
      console.log('Unread activities:', unreadActivitiesCount.value)
    } else {
      console.warn('⚠️ Dashboard: Some services may not be ready yet')
    }

    // Add a click event listener to the document to mark notifications as viewed when clicked
    document.addEventListener('click', (event) => {
      // Check if the clicked element is a notification or contains a notification
      const target = event.target as HTMLElement;
      const notificationElement = target.closest('.notification-badge, .q-badge');

      if (notificationElement) {
        console.log('Notification clicked, marking as viewed');
        // Mark both types of notifications as viewed
        activityNotificationsStore.markConnectionRequestsAsViewed();
        activityNotificationsStore.markActivitiesAsRead();
      }
    })

    // Load conversations for the messages card
    await loadConversations()

    // Load bookmarks data
    await loadBookmarks()
  } catch (error) {
    console.error('Error loading dashboard data:', error)
  } finally {
    isLoading.value = false
  }
})

// Computed
const welcomeMessage = computed(() => {
  if (authStore.currentUser?.email) {
    return `Welcome back, ${authStore.currentUser.email}!`
  }
  return 'Welcome to your dashboard!'
})

// Profile completion computed properties
const profileCompletionPercentage = computed(() => {
  if (!profileStore.currentProfile) return 0

  // Use the stored profile_completion value from the database for consistency
  // This ensures we're showing the same value that's calculated by the profile completion service
  return Math.round(profileStore.currentProfile.profile_completion || 0)
})

// Methods
function getProfileStateColor(state: string | undefined): string {
  if (!state) return 'grey'

  const colors: Record<string, string> = {
    'DRAFT': 'grey',
    'IN_PROGRESS': 'blue',
    'PENDING_APPROVAL': 'orange',
    'ACTIVE': 'green',
    'DISABLED': 'red',
    'DECLINED': 'red-6'
  }

  return colors[state] || 'grey'
}

function getProfileTypeColor(type: string | null | undefined): string {
  if (!type) return 'grey'

  const colors: Record<string, string> = {
    'innovator': 'purple',
    'investor': 'green',
    'mentor': 'blue',
    'professional': 'teal',
    'industry_expert': 'deep-orange',
    'academic_student': 'indigo',
    'academic_institution': 'light-blue',
    'organisation': 'amber'
  }

  return colors[type] || 'grey'
}

function selectProfile(profileId: string): void {
  profileStore.setCurrentProfile(profileId)
}

// View a profile
function viewProfile(profileId: string): void {
  console.log('Viewing profile:', profileId)
  // Navigate to the public profile view
  router.push({ name: 'user-profile', params: { id: profileId } })
}

// Handle messaging a profile
async function messageProfile(profileId: string): Promise<void> {
  console.log('Messaging profile:', profileId)

  // Check if user is authenticated
  const { data } = await supabase.auth.getUser();
  const user = data.user;

  if (!user) {
    notifications.warning('Please sign in to send messages');
    router.push({ name: 'sign-in', query: { redirect: router.currentRoute.value.fullPath } });
    return;
  }

  // Navigate to the profile view with a query parameter to open the message dialog
  router.push({
    name: 'user-profile',
    params: { id: profileId },
    query: { action: 'message' }
  });
}

// Handle following a profile
function followProfile(profileId: string): void {
  console.log('Following profile:', profileId)
  // TODO: Implement follow functionality
  // This would typically update a database record to establish a connection
}

// Handle profile creation events
function handleProfileCreated(profile) {
  console.log('Profile created:', profile)
  // Force reload profiles to update the UI
  profileStore.loadUserProfiles()
}

// Handle loading state changes from profile creation
function handleProfileCreationLoadingChange(isLoading) {
  // This could be used to show a loading indicator if needed
  console.log('Profile creation loading state changed:', isLoading)
}

// Handle remind later for profile completion popup
function handleRemindLater() {
  // Store the current time in localStorage
  localStorage.setItem('profileCompletionRemindLater', Date.now().toString())
  showProfileCompletionPopup.value = false
}

// Profile completion helper methods
function getCompletionColor(percentage: number): string {
  return 'green'
}

function getCompletionStatus(percentage: number): string {
  if (percentage >= 90) return 'Complete'
  if (percentage >= 70) return 'Almost Done'
  if (percentage >= 50) return 'In Progress'
  if (percentage >= 25) return 'Getting Started'
  return 'Just Started'
}

function getCompletedSteps(): number {
  // Calculate completed steps based on profile completion
  const totalSteps = getTotalSteps()
  return Math.floor((profileCompletionPercentage.value / 100) * totalSteps)
}

function getTotalSteps(): number {
  // Total number of profile completion steps
  return 8 // Adjust based on your profile completion requirements
}

function openAIChat() {
  // Trigger AI chat interface
  // You can emit an event or navigate to AI chat page
  console.log('Opening AI Chat...')
  // router.push('/ai-chat')
}

// Navigate to innovation community feed
async function navigateToVirtualCommunity() {
  communityButtonLoading.value = true
  try {
    await router.push('/innovation-community?tab=feed')
  } catch (error) {
    console.error('Navigation error:', error)
  } finally {
    communityButtonLoading.value = false
  }
}

// Handle AI trigger clicks
async function handleAITrigger(triggerKey: string) {
  aiTriggerLoading.value = true
  try {
    await globalServices.aiChatTriggerService.triggerChat(triggerKey, 'dashboard-community')
  } catch (error) {
    console.error('Error triggering AI chat:', error)
  } finally {
    aiTriggerLoading.value = false
  }
}

// Get profile-specific smart suggestions
function getSmartSuggestions() {
  const suggestions = []
  const profile = profileStore.currentProfile
  const profileType = profile?.profile_type

  // Always show profile completion if not complete
  if (!isProfileComplete.value) {
    suggestions.push({
      id: 'complete-profile',
      label: 'Complete Profile',
      icon: 'person_add',
      color: 'primary',
      route: { name: 'profile-edit', params: { id: authStore.currentUser?.id } },
      action: null
    })
  }

  // Profile-specific suggestions
  if (hasAnyProfileData.value && profileType) {
    switch (profileType) {
      case 'startup':
        suggestions.push(
          {
            id: 'find-investors',
            label: 'Find Investors',
            icon: 'trending_up',
            color: 'green',
            route: null,
            action: 'find_investors'
          },
          {
            id: 'marketplace',
            label: 'Opportunities',
            icon: 'store',
            color: 'orange',
            route: '/dashboard/marketplace',
            action: null
          }
        )
        break

      case 'investor':
        suggestions.push(
          {
            id: 'discover-startups',
            label: 'Discover Startups',
            icon: 'rocket_launch',
            color: 'green',
            route: null,
            action: 'discover_startups'
          },
          {
            id: 'community',
            label: 'Community',
            icon: 'people',
            color: 'blue',
            route: '/virtual-community',
            action: null
          }
        )
        break

      case 'mentor':
        suggestions.push(
          {
            id: 'find-mentees',
            label: 'Find Mentees',
            icon: 'school',
            color: 'purple',
            route: null,
            action: 'find_mentees'
          },
          {
            id: 'mentorship',
            label: 'Mentorship Hub',
            icon: 'psychology',
            color: 'indigo',
            route: '/dashboard/mentorship',
            action: null
          }
        )
        break

      case 'corporate':
        suggestions.push(
          {
            id: 'partnerships',
            label: 'Partnerships',
            icon: 'handshake',
            color: 'teal',
            route: null,
            action: 'find_partnerships'
          },
          {
            id: 'marketplace',
            label: 'Partnership Hub',
            icon: 'business',
            color: 'orange',
            route: '/dashboard/marketplace',
            action: null
          }
        )
        break

      default:
        suggestions.push(
          {
            id: 'community',
            label: 'Community',
            icon: 'people',
            color: 'secondary',
            route: '/virtual-community',
            action: null
          },
          {
            id: 'ai-help',
            label: 'AI Help',
            icon: 'psychology',
            color: 'green',
            route: null,
            action: 'profile_enhancement'
          }
        )
    }
  } else {
    // Default suggestions for users without profiles
    suggestions.push(
      {
        id: 'community',
        label: 'Community',
        icon: 'people',
        color: 'secondary',
        route: '/virtual-community',
        action: null
      },
      {
        id: 'ai-help',
        label: 'AI Help',
        icon: 'psychology',
        color: 'green',
        route: null,
        action: 'getting_started'
      }
    )
  }

  // Limit to 3 suggestions for clean layout
  return suggestions.slice(0, 3)
}

// Messaging functions
async function loadConversations() {
  loadingConversations.value = true

  try {
    const result = await messagingStore.loadConversations()
    conversations.value = result
  } catch (err: any) {
    console.error('Error loading conversations:', err)
  } finally {
    loadingConversations.value = false
  }
}

function getUserName(user: any): string {
  return getUniversalUsername(user);
}

function formatDate(dateStr: string): string {
  return date.formatDate(dateStr, 'MMM D, YYYY h:mm A')
}

// Bookmark functions
async function loadBookmarks() {
  loadingBookmarks.value = true
  try {
    await userInteractionsStore.fetchAllUserInteractions()
  } catch (err: any) {
    console.error('Error loading bookmarks:', err)
  } finally {
    loadingBookmarks.value = false
  }
}

function getBookmarkIcon(bookmark: any): string {
  switch (bookmark.type) {
    case 'post':
      return 'article'
    case 'profile':
      return 'person'
    default:
      return 'bookmark'
  }
}

function getBookmarkTitle(bookmark: any): string {
  return bookmark.title || 'Untitled'
}

function getBookmarkType(bookmark: any): string {
  switch (bookmark.type) {
    case 'post':
      return bookmark.content_type === 'marketplace' ? 'Marketplace' : 'Post'
    case 'profile':
      return 'Profile'
    default:
      return 'Content'
  }
}

function viewBookmarkedContent(bookmark: any) {
  if (bookmark.type === 'post') {
    router.push(`/virtual-community/post/${bookmark.post_id}`)
  } else if (bookmark.type === 'profile') {
    router.push(`/virtual-community/user/${bookmark.profile_id}`)
  }
}

// Helper methods for personalized welcome section
function getPersonalizedGreeting(): string {
  const user = authStore.currentUser
  const profile = profileStore.currentProfile

  if (profile?.first_name) {
    return profile.first_name
  } else if (user?.email) {
    return user.email.split('@')[0]
  }
  return 'Innovator'
}

function getPersonalizedSubtitle(): string {
  const profile = profileStore.currentProfile

  if (!hasAnyProfileData.value) {
    return 'Ready to start your innovation journey? Create your profile to connect with Zimbabwe\'s innovation ecosystem.'
  } else if (!isProfileComplete.value) {
    return 'Complete your profile to unlock all features and connect with like-minded innovators.'
  } else {
    const profileType = profile?.profile_type
    switch (profileType) {
      case 'startup':
        return 'Manage your startup journey, connect with investors, and access growth opportunities.'
      case 'investor':
        return 'Discover promising startups, manage your portfolio, and connect with entrepreneurs.'
      case 'mentor':
        return 'Guide the next generation of innovators and share your expertise with the community.'
      case 'corporate':
        return 'Explore innovation partnerships and connect with startups for collaboration.'
      case 'government':
        return 'Support the innovation ecosystem and connect with key stakeholders.'
      case 'academic':
        return 'Bridge academia and industry through research collaboration and knowledge sharing.'
      default:
        return 'Welcome to your innovation dashboard. Explore opportunities and connect with the community.'
    }
  }
}

function getProfileSpecificCTA(): { label: string; route: string; icon: string } {
  const profile = profileStore.currentProfile
  const profileType = profile?.profile_type

  switch (profileType) {
    case 'startup':
      return {
        label: 'Explore Opportunities',
        route: '/innovation-community?tab=marketplace',
        icon: 'trending_up'
      }
    case 'investor':
      return {
        label: 'Discover Startups',
        route: '/innovation-community',
        icon: 'search'
      }
    case 'mentor':
      return {
        label: 'Find Mentees',
        route: '/dashboard/mentorship',
        icon: 'school'
      }
    case 'corporate':
      return {
        label: 'Partnership Hub',
        route: '/innovation-community?tab=marketplace',
        icon: 'handshake'
      }
    default:
      return {
        label: 'View Profile',
        route: '/dashboard/profile',
        icon: 'visibility'
      }
  }
}

// No initialization code needed here - everything is handled in onMounted
</script>

<style scoped>
/* Loading overlay */
.full-page-loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

/* About Page Inspired Welcome Section */
.dashboard-hero {
  background: linear-gradient(135deg, rgba(13, 138, 62, 0.1) 0%, rgba(13, 138, 62, 0.05) 100%),
              url('https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80') center/cover;
  background-size: cover;
  background-position: center;
  min-height: 400px;
  padding: 3rem 0;
  position: relative;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.welcome-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.profile-status-section {
  padding: 16px;
  background: rgba(13, 138, 62, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(13, 138, 62, 0.1);
}

.status-item {
  min-width: 120px;
}

.profile-type-badge {
  font-size: 0.875rem;
  padding: 6px 16px;
  font-weight: 600;
}

.notifications-section {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 8px;
}

.cta-button {
  font-weight: 600;
  padding: 10px 24px;
  border-radius: 8px;
  text-transform: none;
  min-width: 140px;
}

/* Minimal Tools Styling */
.minimal-tools-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  background: #fafafa;
}

.suggestions-row {
  margin-top: 8px;
}

/* Compact Profile Overview Styling */
.profile-overview-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
}

.profile-info-section,
.profile-completion-section {
  padding: 8px 0;
}

.profile-compact-badge {
  font-size: 0.75rem;
  padding: 4px 8px;
  border-radius: 6px;
}

.completion-text {
  flex: 1;
}

/* Consolidated Tools and Overview Cards */
.tools-overview-card,
.activity-overview-card,
.content-overview-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.activity-section,
.connections-section,
.bookmarks-section,
.events-section {
  padding: 12px;
  background: rgba(250, 250, 250, 0.5);
  border-radius: 8px;
}

/* Responsive Design Improvements */
@media (max-width: 768px) {
  .dashboard-hero {
    min-height: 300px;
    padding: 2rem 0;
  }

  .welcome-card .q-card-section {
    padding: 1.5rem !important;
  }

  .profile-status-section .row {
    flex-direction: column;
    gap: 16px;
  }

  .status-item {
    min-width: auto;
  }

  .action-buttons .row {
    flex-direction: column;
    gap: 12px;
  }

  .cta-button {
    width: 100%;
    min-width: auto;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .activity-section,
  .connections-section,
  .bookmarks-section,
  .events-section {
    padding: 8px;
    margin-bottom: 16px;
  }

  .suggestions-row .row {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .dashboard-hero {
    min-height: 250px;
    padding: 1.5rem 0;
  }

  .welcome-card .q-card-section {
    padding: 1rem !important;
  }

  .text-h4 {
    font-size: 1.5rem;
  }

  .profile-type-badge {
    font-size: 0.75rem;
    padding: 4px 12px;
  }

  .notifications-section .row {
    flex-direction: column;
    align-items: center;
  }
}

.welcome-card {
  background-color: #f0f8f1;
  color: #0D8A3E;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Feature column and wrapper */
.feature-column {
  display: flex;
  align-items: center;
}

.feature-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* Feature container with label on top */
.feature-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  background: rgba(13, 138, 62, 0.1);
  border-radius: 8px;
  padding: 16px;
}

.feature-label {
  white-space: nowrap;
  font-weight: 500;
  color: #0D8A3E;
}

.feature-content {
  display: flex;
  align-items: center;
}

/* Mobile-specific feature styles */
.feature-mobile {
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

/* Ensure the feature fits on small screens */
@media (max-width: 599px) {
  .feature-wrapper {
    margin-top: 1rem;
  }
}

/* Profile badges */
.profile-type-badge, .profile-status-badge {
  border-radius: 20px;
  padding: 4px 8px;
}

.profile-type-card, .profile-status-card, .profile-completion-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.profile-completion-card {
  border-left: 4px solid #0D8A3E; /* Green for completion */
}

.profile-type-card {
  border-left: 4px solid var(--q-primary); /* Dynamic color based on profile type */
}

.profile-status-card {
  border-left: 4px solid var(--q-secondary); /* Dynamic color based on profile status */
}

.events-card {
  margin-top: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Progress bar */
.q-linear-progress {
  height: 10px;
  border-radius: 5px;
  overflow: hidden;
}

/* Card styling */
.q-card {
  transition: all 0.2s ease;
  border-radius: 8px;
}

.q-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Dashboard summary cards */
.dashboard-summary-card {
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  transition: all 0.2s ease;
}

.dashboard-summary-card .q-card__section {
  padding: 16px;
}

.dashboard-summary-card .q-card__section.bg-grey-2 {
  padding: 12px 16px;
}

.dashboard-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

/* Message card highlight for unread messages */
.message-card-highlight {
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
  border-left: 4px solid #f44336;
  animation: pulse-red 2s infinite;
}

/* Community action buttons container */
.community-buttons-container {
  width: 100%;
}

/* Dashboard action buttons styling - consistent across all dashboard buttons */
.dashboard-action-btn {
  width: 100%;
  min-height: 44px;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.dashboard-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes pulse-red {
  0% {
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
  }
  50% {
    box-shadow: 0 4px 20px rgba(244, 67, 54, 0.5);
  }
  100% {
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
  }
}

/* Hero Section with Overflow Card */
.dashboard-hero-container {
  position: relative;
  height: 300px;
  margin-bottom: 40px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 75%;
  background: linear-gradient(135deg, rgba(13, 138, 62, 0.1) 0%, rgba(13, 138, 62, 0.05) 100%);
  background-image: url('/images/about-hero.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 0 0 24px 24px;
}

.hero-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(13, 138, 62, 0.8) 0%, rgba(13, 138, 62, 0.6) 100%);
  border-radius: 0 0 24px 24px;
}

.hero-card-container {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: 2;
}

.welcome-card-overflow {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Button Consistency Styles */
.cta-button {
  min-width: 140px;
  border-radius: 8px;
  font-weight: 600;
  text-transform: none;
  transition: all 0.3s ease;
}

.primary-btn {
  background: linear-gradient(135deg, #0D8A3E 0%, #0a7235 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(13, 138, 62, 0.3);
}

.primary-btn:hover {
  background: linear-gradient(135deg, #0a7235 0%, #08612c 100%);
  box-shadow: 0 4px 16px rgba(13, 138, 62, 0.4);
  transform: translateY(-2px);
}

.secondary-btn {
  background: linear-gradient(135deg, #4caf50 0%, #43a047 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.secondary-btn:hover {
  background: linear-gradient(135deg, #43a047 0%, #388e3c 100%);
  box-shadow: 0 4px 16px rgba(76, 175, 80, 0.4);
  transform: translateY(-2px);
}

.ai-btn {
  background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.3);
}

.ai-btn:hover {
  background: linear-gradient(135deg, #8e24aa 0%, #5e35b1 100%);
  box-shadow: 0 4px 16px rgba(156, 39, 176, 0.4);
  transform: translateY(-2px);
}

/* Profile Action Buttons */
.profile-action-btn {
  min-width: 120px;
  border-radius: 6px;
  font-weight: 500;
  text-transform: none;
  transition: all 0.3s ease;
}

.profile-action-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

/* Profile Overview Enhancements */
.profile-overview-card {
  background: linear-gradient(135deg, rgba(13, 138, 62, 0.02) 0%, rgba(255, 255, 255, 1) 100%);
  border-left: 4px solid #0D8A3E;
  border-radius: 16px;
}

.profile-actions {
  display: flex;
  gap: 8px;
}

.profile-type-badge {
  font-size: 0.75rem;
  padding: 6px 12px;
  border-radius: 20px;
}

.profile-state-badge {
  font-size: 0.7rem;
  padding: 4px 8px;
  border-radius: 16px;
}

.completion-section {
  background: rgba(13, 138, 62, 0.03);
  border-radius: 12px;
  padding: 16px;
}

.completion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Dashboard Widget System */
.dashboard-widget {
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  height: 100%;
}

.dashboard-widget:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.widget-header {
  background: rgba(248, 250, 252, 0.8);
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.widget-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 1rem;
  color: #1a202c;
}

.widget-content {
  padding: 16px 20px;
  min-height: 200px;
}

/* Widget Specific Styles */
.activity-widget .widget-header {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.05) 0%, rgba(248, 250, 252, 0.8) 100%);
}

.connections-widget .widget-header {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.05) 0%, rgba(248, 250, 252, 0.8) 100%);
}

.bookmarks-widget .widget-header {
  background: linear-gradient(135deg, rgba(0, 188, 212, 0.05) 0%, rgba(248, 250, 252, 0.8) 100%);
}

.events-widget .widget-header {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(248, 250, 252, 0.8) 100%);
}

.announcements-widget .widget-header {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.05) 0%, rgba(248, 250, 252, 0.8) 100%);
}

.messages-widget .widget-header {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.05) 0%, rgba(248, 250, 252, 0.8) 100%);
}

.messages-widget.widget-highlight .widget-header {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(255, 235, 238, 0.8) 100%);
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: #64748b;
}

/* List Item Enhancements */
.bookmark-item,
.event-item,
.announcement-item,
.message-item {
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.bookmark-item:hover,
.event-item:hover,
.announcement-item:hover,
.message-item:hover {
  background: rgba(248, 250, 252, 0.8);
  transform: translateX(4px);
}

.message-unread {
  background: rgba(244, 67, 54, 0.05);
  border-left: 3px solid #f44336;
}

/* My Profiles Card */
.my-profiles-card {
  background: linear-gradient(135deg, rgba(13, 138, 62, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
  border-radius: 16px;
}

.profile-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  background: white;
}

.profile-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.profile-card.active {
  border-color: #0D8A3E;
  box-shadow: 0 4px 20px rgba(13, 138, 62, 0.15);
}

.profile-type-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 1;
}

.profile-completion-indicator {
  position: absolute;
  bottom: 12px;
  right: 12px;
  z-index: 1;
}

.profile-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-hero-container {
    height: 250px;
    margin-bottom: 80px;
  }

  .hero-card-container {
    padding: 0 16px;
  }

  .welcome-card-overflow {
    border-radius: 16px;
  }

  .widget-content {
    min-height: 150px;
    padding: 12px 16px;
  }

  .widget-header {
    padding: 12px 16px;
  }

  .completion-section {
    padding: 12px;
  }

  .profile-actions {
    flex-direction: column;
    gap: 4px;
  }
}

/* Utility Classes */
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-primary {
  color: #0D8A3E !important;
}

.text-blue {
  color: #2196f3 !important;
}

.text-purple {
  color: #9c27b0 !important;
}

.text-cyan {
  color: #00bcd4 !important;
}

.text-green {
  color: #4caf50 !important;
}

/* Smart Suggestion Chips */
.smart-suggestion-chip {
  transition: all 0.3s ease;
  font-weight: 500;
}

.smart-suggestion-chip:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.text-orange {
  color: #ff9800 !important;
}

.text-red {
  color: #f44336 !important;
}
</style>
