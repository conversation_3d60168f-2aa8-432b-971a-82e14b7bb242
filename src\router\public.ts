import { RouteRecordRaw } from 'vue-router'
// Removed import for DatabaseTest.vue

// Public routes (accessible without authentication)
const publicRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import(/* webpackChunkName: "layout-main" */ '../layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        name: 'home',
        component: () => import('../views/public/Home.vue')
      },
      {
        path: 'sign-in',
        name: 'sign-in',
        component: () => import('../views/SignIn.vue'),
        meta: { guestOnly: true }
      },
      {
        path: 'password-reset',
        name: 'password-reset',
        component: () => import('../views/public/auth/PasswordReset.vue'),
        meta: { guestOnly: true }
      },
      {
        path: 'reset-password-confirm',
        name: 'reset-password-confirm',
        component: () => import('../views/public/auth/ResetPasswordConfirm.vue'),
        meta: { guestOnly: true }
      },
      // Aliases for Supabase auth redirects
      {
        path: '/reset-password-confirm',
        redirect: { name: 'reset-password-confirm' }
      },
      {
        path: '/auth/callback',
        redirect: { name: 'auth-callback' }
      },
      {
        path: '/auth/reset-password',
        redirect: { name: 'reset-password-confirm' }
      },
      {
        path: '/verify-email',
        redirect: { name: 'verify-email' }
      },
      {
        path: 'news',
        name: 'news',
        redirect: { path: '/innovation-community', query: { tab: 'blog' } }
      },
      {
        path: 'blog',
        name: 'blog-redirect',
        redirect: { path: '/innovation-community', query: { tab: 'blog' } }
      },
      {
        path: 'news/:id',
        name: 'single-news',
        component: () => import('../components/public/SingleStoryView.vue')
      },
      {
        path: '/articles/:slug',
        name: 'article',
        component: () => import('../views/public/content/SingleBlogLayout.vue'),
        props: true
      },
      {
        path: '/articles/academic-collaboration',
        name: 'academic-collaboration',
        component: () => import('../views/public/content/ArticleAcademicCollaboration.vue')
      },
      {
        path: 'events',
        name: 'events',
        component: () => import('../views/public/content/Events.vue')
      },
      {
        path: 'auth/callback',
        name: 'auth-callback',
        component: () => import('../views/public/auth/AuthCallback.vue')
      },
      {
        path: 'auth/verify',
        name: 'verify-email',
        component: () => import('../views/public/auth/VerifyEmail.vue')
      },
      // Removed db-test route that was causing issues
      {
        path: 'about',
        name: 'about',
        component: () => import('../views/public/About.vue')
      },
      {
        path: 'virtual-community',
        name: 'virtual-community',
        component: () => import(/* webpackChunkName: "virtual-community" */ '../views/public/VirtualCommunityView.vue')
      },

      {
        path: 'simple-test',
        name: 'simple-test',
        component: () => import(/* webpackChunkName: "simple-test" */ '../views/public/SimpleTestLayoutView.vue')
      },

      {
        path: 'virtual-community/article/:slug',
        name: 'virtual-community-article',
        component: () => import('../views/public/content/SingleBlogLayout.vue'),
        props: true
      },
      {
        path: 'virtual-community/group/:id',
        name: 'group',
        component: () => import('../views/public/GroupView.vue'),
        props: true
      },
      {
        path: 'virtual-community/marketplace/:id',
        name: 'marketplace-listing',
        component: () => import('../views/public/content/SinglePostView.vue'),
        props: true
      },
      {
        path: 'virtual-community/marketplace/:id/edit',
        name: 'edit-marketplace-listing',
        component: () => import('../views/public/MarketplaceEditView.vue'),
        props: true,
        meta: { requiresAuth: true }
      },
      {
        path: 'virtual-community/post/:id',
        name: 'post-details',
        component: () => import('../views/public/content/SinglePostView.vue'),
        props: true
      },
      {
        path: 'virtual-community/event/:id',
        name: 'event-details',
        component: () => import('../views/public/content/SinglePostView.vue'),
        props: true
      },
      {
        path: 'virtual-community/opportunity/:id',
        name: 'opportunity-details',
        component: () => import('../views/public/content/SinglePostView.vue'),
        props: true
      },
      {
        path: 'virtual-community/user/:id',
        name: 'user-profile',
        component: () => import('../views/public/content/UserProfileView.vue'),
        props: true
      },
      {
        path: 'opportunities-feed',
        name: 'opportunities-feed',
        redirect: { path: '/innovation-community', query: { tab: 'feed' } }
      },
      {
        path: 'hub-facilities',
        name: 'hub-facilities',
        component: () => import('../views/public/HubFacilities.vue')
      },
      // Redirect hub-events to hub-facilities since they're now combined
      {
        path: 'hub-events',
        redirect: { name: 'hub-facilities' }
      },
      {
        path: 'contact-us',
        name: 'contact-us',
        component: () => import('../views/public/ContactUs.vue')
      },
      // Legal Pages
      {
        path: 'legal/privacy-policy',
        name: 'privacy-policy',
        component: () => import(/* webpackChunkName: "legal" */ '../views/legal/PrivacyPolicy.vue'),
        meta: {
          title: 'Privacy Policy - SmileFactory',
          description: 'Learn how SmileFactory protects your privacy and handles your personal data.'
        }
      },
      {
        path: 'legal/terms-conditions',
        name: 'terms-conditions',
        component: () => import(/* webpackChunkName: "legal" */ '../views/legal/TermsAndConditions.vue'),
        meta: {
          title: 'Terms and Conditions - SmileFactory',
          description: 'Read our terms of service and platform usage guidelines.'
        }
      },
      {
        path: 'legal/gdpr-compliance',
        name: 'gdpr-compliance',
        component: () => import(/* webpackChunkName: "legal" */ '../views/legal/GDPRCompliance.vue'),
        meta: {
          title: 'GDPR Compliance & Cookie Policy - SmileFactory',
          description: 'Learn about our GDPR compliance and cookie usage policies.'
        }
      },
      {
        path: 'legal/faq',
        name: 'faq',
        component: () => import(/* webpackChunkName: "legal" */ '../views/legal/FAQ.vue'),
        meta: {
          title: 'FAQ - Frequently Asked Questions | SmileFactory',
          description: 'Find answers to common questions about our platform and services.'
        }
      },
      {
        path: 'legal/cookie-preferences',
        name: 'cookie-preferences',
        component: () => import(/* webpackChunkName: "legal" */ '../views/legal/CookiePreferences.vue'),
        meta: {
          title: 'Cookie Preferences - SmileFactory',
          description: 'Manage your cookie preferences and control how we use cookies.'
        }
      },

    ]
  },
  {
    path: '/innovation-community',
    component: () => import(/* webpackChunkName: "layout-virtual-community" */ '../layouts/VirtualCommunityLayout.vue'),
    children: [
      {
        path: '',
        name: 'innovation-community',
        component: () => import(/* webpackChunkName: "new-virtual-community" */ '../views/public/NewVirtualCommunityLayoutView.vue')
      }
    ]
  },
  {
    path: '/onboarding',
    component: () => import(/* webpackChunkName: "layout-main" */ '../layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'create-profile',
        name: 'initial-profile-creation',
        component: () => import(/* webpackChunkName: "profile" */ '../views/dashboard/ProfileCompletion.vue'),
        meta: { requiresNoProfile: true }
      }
    ]
  },
]

export default publicRoutes