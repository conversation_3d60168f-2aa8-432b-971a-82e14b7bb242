<template>
  <div class="blog-post-form">
    <q-form @submit="handleSubmit" class="q-gutter-md">
      <!-- Title -->
      <q-input
        v-model="formData.title"
        label="Title"
        outlined
        :rules="[val => !!val || 'Title is required', val => val.length <= 100 || 'Maximum 100 characters']"
        counter
        maxlength="100"
      />

      <!-- Excerpt -->
      <q-input
        v-model="formData.excerpt"
        label="Excerpt"
        outlined
        :rules="[val => !!val || 'Excerpt is required', val => val.length <= 200 || 'Maximum 200 characters']"
        counter
        maxlength="200"
        hint="A brief summary of your article"
      />

      <!-- Category -->
      <q-select
        v-model="formData.category"
        :options="blogCategoryOptions"
        label="Category"
        outlined
        emit-value
        map-options
        :rules="[val => !!val || 'Category is required']"
        @update:model-value="handleCategoryChange"
      >
        <template v-slot:prepend>
          <q-icon name="category" />
        </template>
      </q-select>

      <!-- Custom Category (appears when "Other" is selected) -->
      <q-input
        v-if="formData.category === 'other'"
        v-model="formData.customCategory"
        label="Specify Category"
        outlined
        class="q-mt-sm"
        :rules="[val => !!val || 'Custom category is required']"
      >
        <template v-slot:prepend>
          <q-icon name="edit" />
        </template>
      </q-input>

      <!-- Content - WYSIWYG Editor -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-xs">Content</div>
        <div class="editor-container" :class="{ 'editor-error': contentError }">
          <q-editor
            v-model="formData.content"
            min-height="300px"
            :toolbar="[
              ['bold', 'italic', 'strike', 'underline'],
              ['token', 'hr', 'link', 'custom_btn'],
              [
                {
                  label: 'Font Size',
                  icon: 'format_size',
                  list: 'no-icons',
                  options: ['size-1', 'size-2', 'size-3', 'size-4', 'size-5', 'size-6', 'size-7']
                }
              ],
              ['quote', 'unordered', 'ordered', 'outdent', 'indent'],
              ['undo', 'redo', 'fullscreen'],
              ['viewsource']
            ]"
            :rules="[val => !!val || 'Content is required']"
            @update:model-value="validateContent"
          />
        </div>
        <div v-if="contentError" class="text-negative text-caption q-mt-xs">
          Content is required
        </div>
      </div>

      <!-- Tags Display (moved above inputs) -->
      <div v-if="formData.tags.length > 0" class="q-mb-md">
        <div class="text-subtitle2 q-mb-xs">Selected Tags:</div>
        <div class="row q-gutter-xs">
          <q-chip
            v-for="(tag, index) in formData.tags"
            :key="index"
            removable
            @remove="removeTag(index)"
            color="primary"
            text-color="white"
            size="md"
          >
            {{ tag }}
          </q-chip>
        </div>
      </div>

      <!-- Predefined Tags -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-xs">Tags (select multiple)</div>
        <q-select
          v-model="selectedPredefinedTag"
          :options="predefinedTagsFiltered"
          label="Select from predefined tags"
          outlined
          clearable
          use-input
          hide-selected
          fill-input
          @filter="filterTags"
          @update:model-value="addPredefinedTag"
        >
          <template v-slot:prepend>
            <q-icon name="local_offer" />
          </template>
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">
                No results
              </q-item-section>
            </q-item>
          </template>
          <template v-slot:option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section>
                <q-item-label>{{ scope.opt.label }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
          <template v-slot:after>
            <q-badge color="primary" floating>
              {{ formData.tags.length }}
            </q-badge>
          </template>
        </q-select>
        <div class="text-caption text-grey q-mt-xs">
          Select a tag and click to add it. You can add multiple tags.
        </div>
      </div>

      <!-- Custom Tags -->
      <q-input
        v-model="tagsInput"
        label="Or add custom tags (comma-separated)"
        outlined
        hint="Enter tags separated by commas"
        @blur="processTags"
      >
        <template v-slot:prepend>
          <q-icon name="add_circle" />
        </template>
      </q-input>

      <!-- Read Time -->
      <q-select
        v-model="formData.readTime"
        :options="readTimeOptions"
        label="Estimated Read Time"
        outlined
        emit-value
        map-options
      >
        <template v-slot:prepend>
          <q-icon name="schedule" />
        </template>
      </q-select>

      <!-- Image Upload -->
      <div class="q-mb-md">
        <q-file
          v-model="imageFile"
          label="Add Cover Image"
          outlined
          accept=".jpg, .jpeg, .png, .gif"
          @update:model-value="handleImageUpload"
          max-file-size="5242880"
          @rejected="onRejected"
          :rules="[val => !!val || 'Cover image is required']"
        >
          <template v-slot:prepend>
            <q-icon name="attach_file" />
          </template>
        </q-file>

        <!-- Image Preview -->
        <div v-if="formData.image" class="image-preview q-mt-sm">
          <q-img :src="formData.image" style="max-height: 200px; max-width: 100%;" />
          <q-btn
            round
            color="negative"
            icon="delete"
            size="sm"
            class="absolute-top-right"
            @click="removeImage"
          />
        </div>
      </div>

      <!-- Visibility -->
      <div class="q-mb-md">
        <q-select
          v-model="formData.visibility"
          :options="visibilityOptions"
          label="Visibility"
          outlined
          emit-value
          map-options
        >
          <template v-slot:prepend>
            <q-icon name="visibility" />
          </template>
        </q-select>
      </div>

      <!-- Action Buttons -->
      <div class="row justify-end q-gutter-sm">
        <q-btn
          label="Cancel"
          color="grey"
          flat
          @click="$emit('cancel')"
        />
        <q-btn
          label="Save Draft"
          color="secondary"
          outline
          @click="saveDraft"
          :loading="loading"
          :disable="loading"
        >
          <template v-slot:loading>
            <q-spinner-dots size="24px" />
          </template>
        </q-btn>
        <q-btn
          label="Publish"
          type="submit"
          color="primary"
          :loading="loading"
          :disable="loading"
        >
          <template v-slot:loading>
            <q-spinner-dots size="24px" />
          </template>
        </q-btn>
      </div>
    </q-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useSecurity } from '@/composables/useSecurity';

const emit = defineEmits(['submit', 'cancel']);
const $q = useQuasar();
const { sanitizeContent, sanitizeInput, initCSRF, getCurrentCSRFToken } = useSecurity();

// State
const loading = ref(false);
const imageFile = ref(null);
const tagsInput = ref('');
const selectedPredefinedTag = ref(null);
const contentError = ref(false);
const predefinedTagsFiltered = ref([]); // Add this missing variable
const formData = ref({
  title: '',
  excerpt: '',
  content: '',
  image: null,
  category: '',
  customCategory: '',
  tags: [],
  readTime: '5',
  visibility: 'public',
  postType: 'blog',
  isDraft: false
});

// Validate content field with security checks
function validateContent(val: string) {
  contentError.value = !val || val.trim() === '';

  // Additional security validation
  if (val && val.length > 50000) { // Reasonable limit for blog content
    contentError.value = true;
    return 'Content is too long (maximum 50,000 characters)';
  }

  return contentError.value ? 'Content is required' : true;
}

// Options
const blogCategoryOptions = [
  { label: 'Innovation Insights', value: 'innovation', icon: 'lightbulb' },
  { label: 'Entrepreneurship', value: 'entrepreneurship', icon: 'rocket_launch' },
  { label: 'Technology Trends', value: 'technology', icon: 'trending_up' },
  { label: 'Funding & Investment', value: 'funding', icon: 'attach_money' },
  { label: 'Success Stories', value: 'success_stories', icon: 'emoji_events' },
  { label: 'Research & Development', value: 'research', icon: 'science' },
  { label: 'Industry News', value: 'industry_news', icon: 'feed' },
  { label: 'Interviews', value: 'interviews', icon: 'record_voice_over' },
  { label: 'How-to Guides', value: 'guides', icon: 'menu_book' },
  { label: 'Events & Conferences', value: 'events', icon: 'event' },
  { label: 'Artificial Intelligence', value: 'ai', icon: 'smart_toy' },
  { label: 'Blockchain & Web3', value: 'blockchain', icon: 'link' },
  { label: 'Sustainability', value: 'sustainability', icon: 'eco' },
  { label: 'Digital Transformation', value: 'digital_transformation', icon: 'transform' },
  { label: 'Health Innovation', value: 'health_innovation', icon: 'health_and_safety' },
  { label: 'AgriTech', value: 'agritech', icon: 'grass' },
  { label: 'FinTech', value: 'fintech', icon: 'payments' },
  { label: 'EdTech', value: 'edtech', icon: 'school' },
  { label: 'Other', value: 'other', icon: 'more_horiz' }
];

// Handle category change
function handleCategoryChange(value: string) {
  // If category is not "other", clear the custom category
  if (value !== 'other') {
    formData.value.customCategory = '';
  }
}

const readTimeOptions = [
  { label: 'Under 5 minutes', value: '3' },
  { label: '5 minutes', value: '5' },
  { label: '10 minutes', value: '10' },
  { label: '15+ minutes', value: '15' }
];

const visibilityOptions = [
  { label: 'Public', value: 'public', icon: 'public' },
  { label: 'Connections Only', value: 'connections', icon: 'people' },
  { label: 'Private', value: 'private', icon: 'lock' }
];

// Predefined tags based on existing tags in the database and blog categories
const predefinedTagOptions = [
  // Innovation & Technology
  { label: 'innovation', value: 'innovation' },
  { label: 'technology', value: 'technology' },
  { label: 'digital-transformation', value: 'digital-transformation' },
  { label: 'future-tech', value: 'future-tech' },
  { label: 'emerging-tech', value: 'emerging-tech' },
  { label: 'tech-trends', value: 'tech-trends' },
  { label: 'digital', value: 'digital' },

  // AI & Data
  { label: 'artificial-intelligence', value: 'artificial-intelligence' },
  { label: 'machine-learning', value: 'machine-learning' },
  { label: 'deep-learning', value: 'deep-learning' },
  { label: 'data-science', value: 'data-science' },
  { label: 'big-data', value: 'big-data' },
  { label: 'AI', value: 'AI' },
  { label: 'neural-networks', value: 'neural-networks' },
  { label: 'computer-vision', value: 'computer-vision' },
  { label: 'natural-language-processing', value: 'natural-language-processing' },

  // Blockchain & Web3
  { label: 'blockchain', value: 'blockchain' },
  { label: 'web3', value: 'web3' },
  { label: 'cryptocurrency', value: 'cryptocurrency' },
  { label: 'smart-contracts', value: 'smart-contracts' },
  { label: 'decentralized', value: 'decentralized' },
  { label: 'nft', value: 'nft' },
  { label: 'defi', value: 'defi' },

  // Business & Entrepreneurship
  { label: 'startups', value: 'startups' },
  { label: 'entrepreneurship', value: 'entrepreneurship' },
  { label: 'business', value: 'business' },
  { label: 'leadership', value: 'leadership' },
  { label: 'management', value: 'management' },
  { label: 'strategy', value: 'strategy' },
  { label: 'innovation-strategy', value: 'innovation-strategy' },
  { label: 'business-model', value: 'business-model' },
  { label: 'scaling', value: 'scaling' },
  { label: 'growth', value: 'growth' },

  // Funding & Investment
  { label: 'funding', value: 'funding' },
  { label: 'investment', value: 'investment' },
  { label: 'venture-capital', value: 'venture-capital' },
  { label: 'angel-investing', value: 'angel-investing' },
  { label: 'seed-funding', value: 'seed-funding' },
  { label: 'crowdfunding', value: 'crowdfunding' },
  { label: 'grants', value: 'grants' },
  { label: 'finance', value: 'finance' },

  // Industry Specific
  { label: 'fintech', value: 'fintech' },
  { label: 'agritech', value: 'agritech' },
  { label: 'healthtech', value: 'healthtech' },
  { label: 'edtech', value: 'edtech' },
  { label: 'cleantech', value: 'cleantech' },
  { label: 'biotech', value: 'biotech' },
  { label: 'proptech', value: 'proptech' },
  { label: 'insurtech', value: 'insurtech' },
  { label: 'regtech', value: 'regtech' },

  // Sustainability & Social Impact
  { label: 'sustainability', value: 'sustainability' },
  { label: 'climate-tech', value: 'climate-tech' },
  { label: 'social-impact', value: 'social-impact' },
  { label: 'esg', value: 'esg' },
  { label: 'green-tech', value: 'green-tech' },
  { label: 'circular-economy', value: 'circular-economy' },
  { label: 'renewable-energy', value: 'renewable-energy' },

  // Research & Development
  { label: 'research', value: 'research' },
  { label: 'development', value: 'development' },
  { label: 'r&d', value: 'r&d' },
  { label: 'innovation-lab', value: 'innovation-lab' },
  { label: 'prototyping', value: 'prototyping' },
  { label: 'product-development', value: 'product-development' },
  { label: 'academic', value: 'academic' },
  { label: 'scientific-research', value: 'scientific-research' },

  // Collaboration & Community
  { label: 'mentorship', value: 'mentorship' },
  { label: 'collaboration', value: 'collaboration' },
  { label: 'partnership', value: 'partnership' },
  { label: 'community', value: 'community' },
  { label: 'ecosystem', value: 'ecosystem' },
  { label: 'networking', value: 'networking' },
  { label: 'co-creation', value: 'co-creation' },

  // Events & Activities
  { label: 'workshop', value: 'workshop' },
  { label: 'event', value: 'event' },
  { label: 'conference', value: 'conference' },
  { label: 'hackathon', value: 'hackathon' },
  { label: 'webinar', value: 'webinar' },
  { label: 'meetup', value: 'meetup' },
  { label: 'training', value: 'training' },

  // Regional & Geographic
  { label: 'zimbabwe', value: 'zimbabwe' },
  { label: 'africa', value: 'africa' },
  { label: 'global', value: 'global' },
  { label: 'local', value: 'local' },
  { label: 'regional', value: 'regional' },
  { label: 'harare', value: 'harare' },

  // Content Types
  { label: 'case-study', value: 'case-study' },
  { label: 'success-story', value: 'success-story' },
  { label: 'interview', value: 'interview' },
  { label: 'how-to', value: 'how-to' },
  { label: 'guide', value: 'guide' },
  { label: 'opinion', value: 'opinion' },
  { label: 'analysis', value: 'analysis' },
  { label: 'news', value: 'news' },
  { label: 'announcement', value: 'announcement' },
  { label: 'thought-leadership', value: 'thought-leadership' }
];

// Initialize predefinedTagsFiltered with all tags when component is mounted
onMounted(() => {
  predefinedTagsFiltered.value = [...predefinedTagOptions];
  // Initialize CSRF protection
  initCSRF();
});

// Methods
function handleImageUpload(file: File) {
  if (!file) return;

  // Create a data URL for preview
  const reader = new FileReader();
  reader.onload = (e: ProgressEvent<FileReader>) => {
    if (e.target && e.target.result) {
      formData.value.image = e.target.result as string;
    }
  };
  reader.readAsDataURL(file);

  // Note: The actual upload to Supabase Storage will happen in the posts store
  // when the post is created. We just store the data URL here for preview.
}

function removeImage() {
  formData.value.image = null;
  imageFile.value = null;
}

interface RejectedEntry {
  failedPropValidation: string;
  file: File;
}

function onRejected(rejectedEntries: RejectedEntry[]) {
  // Display notification for rejected files
  rejectedEntries.forEach((entry: RejectedEntry) => {
    let message = '';
    if (entry.failedPropValidation === 'accept') {
      message = 'Please upload only image files (JPG, PNG, GIF)';
    } else if (entry.failedPropValidation === 'max-file-size') {
      message = 'File is too large. Maximum size is 5MB';
    } else {
      message = 'File upload failed';
    }

    $q.notify({
      type: 'negative',
      message
    });
  });
}

// Filter predefined tags based on user input
function filterTags(val: string, update: (callback: () => void) => void) {
  if (val === '') {
    update(() => {
      // Show all options when no search term
      predefinedTagsFiltered.value = predefinedTagOptions;
      return predefinedTagOptions;
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    // Filter options based on search term
    const filteredOptions = predefinedTagOptions.filter(
      v => v.label.toLowerCase().indexOf(needle) > -1
    );
    predefinedTagsFiltered.value = filteredOptions;
    return filteredOptions;
  });
}

// Add a predefined tag to the list
function addPredefinedTag(tag: any) {
  if (!tag) return;

  // Extract the tag value if it's an object
  let tagValue: string;
  if (typeof tag === 'object' && tag !== null) {
    // Use the value property if available, otherwise use label
    tagValue = tag.value || tag.label || '';
  } else {
    tagValue = String(tag);
  }

  // Add the tag if it's not already in the list
  if (tagValue && !formData.value.tags.includes(tagValue)) {
    formData.value.tags.push(tagValue);
  }

  // Clear the selection for next use
  selectedPredefinedTag.value = null;
}

// Process manually entered tags
function processTags() {
  if (!tagsInput.value) return;

  // Split by comma and trim whitespace
  const newTags = tagsInput.value.split(',')
    .map(tag => tag.trim())
    .filter(tag => tag && !formData.value.tags.includes(tag)); // Only add tags that don't already exist

  // Add new tags to the existing tags
  if (newTags.length > 0) {
    formData.value.tags = [...formData.value.tags, ...newTags];
  }

  // Clear the input
  tagsInput.value = '';
}

// Remove a tag from the list
function removeTag(index: number) {
  formData.value.tags.splice(index, 1);
}

function saveDraft() {
  formData.value.isDraft = true;
  handleSubmit();
}

async function handleSubmit() {
  loading.value = true;

  // Validate content
  validateContent(formData.value.content);
  if (contentError.value) {
    loading.value = false;
    $q.notify({
      color: 'negative',
      message: 'Please add content to your blog post',
      icon: 'warning'
    });
    return;
  }

  // SECURITY: Sanitize all user inputs before submission
  const sanitizedData = {
    ...formData.value,
    title: sanitizeInput(formData.value.title, 200),
    excerpt: sanitizeInput(formData.value.excerpt, 500),
    content: sanitizeContent(formData.value.content),
    category: sanitizeInput(formData.value.category, 50),
    customCategory: sanitizeInput(formData.value.customCategory, 50),
    tags: formData.value.tags.map(tag => sanitizeInput(String(tag), 50))
  };

  // Process tags one more time before submission
  processTags();

  // Automatically add main category and subcategory as tags
  // For blogs, the main category is always 'blog'
  if (!formData.value.tags.includes('blog')) {
    formData.value.tags.push('blog');
  }

  // Handle custom category if "other" is selected
  let finalCategory = formData.value.category;
  if (formData.value.category === 'other') {
    if (!formData.value.customCategory) {
      loading.value = false;
      $q.notify({
        color: 'negative',
        message: 'Please specify a custom category',
        icon: 'warning'
      });
      return;
    }
    // Use the custom category value but keep 'other' as the internal value
    finalCategory = formData.value.customCategory.toLowerCase().replace(/\s+/g, '_');

    // Add the custom category as a tag
    if (!formData.value.tags.includes(finalCategory)) {
      formData.value.tags.push(finalCategory);
    }
  } else if (formData.value.category) {
    // Add the selected category as a tag if it exists and is not already included
    const categoryTag = formData.value.category.toLowerCase();
    if (!formData.value.tags.includes(categoryTag)) {
      formData.value.tags.push(categoryTag);
    }
  }

  // If still no tags were added, add a default 'blog' tag
  if (!formData.value.tags || formData.value.tags.length === 0) {
    formData.value.tags = ['blog'];
  }

  try {

    // Make sure we have at least one tag
    if (formData.value.tags.length === 0) {
      // Add a default tag based on the category
      formData.value.tags.push(formData.value.category || 'general');
    }

    // Generate a slug from the title
    const slug = formData.value.title
      .toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-');

    // Process tags to ensure they're all strings (not objects)
    const processedTags = formData.value.tags.map(tag => {
      if (typeof tag === 'object' && tag !== null) {
        return tag.label || tag.value || String(tag);
      }
      return String(tag);
    });

    // Create post object with postType and subType using sanitized data
    const post = {
      ...sanitizedData,
      // Frontend fields for UI categorization
      postType: 'blog',
      subType: 'blog', // This is important for filtering

      // Database fields - post_type must be 'platform', 'admin', or 'automated' per DB constraint
      post_type: 'platform', // User-created posts are always 'platform'
      sub_type: 'blog', // This identifies it as a blog post

      featuredImage: sanitizedData.image,
      // Blog-specific fields
      blog_title: sanitizedData.title,
      blog_category: finalCategory, // Use the finalCategory which handles custom categories
      blogCategory: finalCategory, // For frontend compatibility
      blogFullContent: sanitizedData.content,
      content: sanitizedData.content, // Make sure content is set
      status: sanitizedData.isDraft ? 'draft' : 'published', // Must be lowercase to match database constraint
      createdAt: new Date().toISOString(),
      likesCount: 0,
      commentsCount: 0,
      tags: processedTags, // Use processed tags
      slug,
      // Add CSRF token for security
      csrfToken: getCurrentCSRFToken()
    };

    emit('submit', post);
  } catch (error) {
    console.error('Error creating blog post:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to create blog post. Please try again.'
    });
  } finally {
    // Add a small delay to show the loading state
    setTimeout(() => {
      loading.value = false;
    }, 500);
  }
}
</script>

<style scoped>
.image-preview {
  position: relative;
  display: inline-block;
}

.editor-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.editor-error {
  border-color: #C10015;
}

:deep(.q-editor) {
  border: none;
}
</style>
