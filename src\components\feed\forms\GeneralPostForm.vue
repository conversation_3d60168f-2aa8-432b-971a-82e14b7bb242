<template>
  <div class="general-post-form">
    <q-form @submit="handleSubmit" class="q-gutter-md">
      <!-- Content -->
      <q-input
        v-model="formData.content"
        type="textarea"
        label="What's on your mind?"
        outlined
        autogrow
        :rules="[val => !!val || 'Content is required', val => val.length <= 2000 || 'Maximum 2000 characters']"
        counter
        maxlength="2000"
        class="q-mb-md"
      />

      <!-- Image Upload -->
      <div class="q-mb-md">
        <q-file
          v-model="imageFile"
          label="Add Image (optional)"
          outlined
          accept=".jpg, .jpeg, .png, .gif"
          @update:model-value="handleImageUpload"
          max-file-size="5242880"
          @rejected="onRejected"
        >
          <template v-slot:prepend>
            <q-icon name="attach_file" />
          </template>
        </q-file>

        <!-- Image Preview -->
        <div v-if="formData.image" class="image-preview q-mt-sm">
          <q-img :src="formData.image" style="max-height: 200px; max-width: 100%;" />
          <q-btn
            round
            color="negative"
            icon="delete"
            size="sm"
            class="absolute-top-right"
            @click="removeImage"
          />
        </div>
      </div>

      <!-- Tags Display (moved above inputs) -->
      <div v-if="formData.tags && formData.tags.length > 0" class="q-mb-md">
        <div class="text-subtitle2 q-mb-xs">Selected Tags:</div>
        <div class="row q-gutter-xs">
          <q-chip
            v-for="(tag, index) in formData.tags"
            :key="index"
            removable
            @remove="removeTag(index)"
            color="primary"
            text-color="white"
            size="md"
          >
            {{ tag }}
          </q-chip>
        </div>
      </div>

      <!-- Predefined Tags -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-xs">Tags (select multiple)</div>
        <q-select
          v-model="selectedPredefinedTag"
          :options="predefinedTagsFiltered"
          label="Select from predefined tags"
          outlined
          clearable
          use-input
          hide-selected
          fill-input
          @filter="filterTags"
          @update:model-value="addPredefinedTag"
        >
          <template v-slot:prepend>
            <q-icon name="local_offer" />
          </template>
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">
                No results
              </q-item-section>
            </q-item>
          </template>
          <template v-slot:after>
            <q-badge color="primary" floating>
              {{ formData.tags.length }}
            </q-badge>
          </template>
        </q-select>
        <div class="text-caption text-grey q-mt-xs">
          Select a tag and click to add it. You can add multiple tags.
        </div>
      </div>

      <!-- Custom Tags -->
      <q-input
        v-model="tagsInput"
        label="Or add custom tags (comma-separated)"
        outlined
        hint="Enter tags separated by commas"
        @blur="processTags"
      >
        <template v-slot:prepend>
          <q-icon name="add_circle" />
        </template>
      </q-input>

      <!-- Visibility -->
      <div class="q-mb-md">
        <q-select
          v-model="formData.visibility"
          :options="visibilityOptions"
          label="Visibility"
          outlined
          emit-value
          map-options
        >
          <template v-slot:prepend>
            <q-icon name="visibility" />
          </template>
        </q-select>
      </div>

      <!-- Action Buttons -->
      <div class="row justify-end q-gutter-sm">
        <q-btn
          label="Cancel"
          color="grey"
          flat
          @click="$emit('cancel')"
        />
        <q-btn
          label="Post"
          type="submit"
          color="primary"
          :loading="loading"
          :disable="loading"
        >
          <template v-slot:loading>
            <q-spinner-dots size="24px" />
          </template>
        </q-btn>
      </div>
    </q-form>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, watch } from 'vue';
import { useQuasar } from 'quasar';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['submit', 'cancel']);
const $q = useQuasar();

// State
const localLoading = ref(false);

// Computed loading state that combines local and parent loading
const loading = ref(false);

// Watch for changes in parent loading state
watch(() => props.loading, (newVal) => {
  loading.value = newVal || localLoading.value;
});

// Watch for changes in local loading state
watch(localLoading, (newVal) => {
  loading.value = newVal || props.loading;
});
const imageFile = ref(null);
const tagsInput = ref('');
const selectedPredefinedTag = ref(null);
const formData = ref({
  content: '',
  image: null,
  tags: [],
  visibility: 'public',
  postType: 'general'
});

// Options
const visibilityOptions = [
  { label: 'Public', value: 'public', icon: 'public' },
  { label: 'Connections Only', value: 'connections', icon: 'people' },
  { label: 'Private', value: 'private', icon: 'lock' }
];

// Predefined tags for general posts
const predefinedTagOptions = [
  // Topic-based tags
  { label: 'question', value: 'question' },
  { label: 'discussion', value: 'discussion' },
  { label: 'announcement', value: 'announcement' },
  { label: 'idea', value: 'idea' },
  { label: 'feedback', value: 'feedback' },
  { label: 'help', value: 'help' },
  { label: 'advice', value: 'advice' },
  { label: 'opinion', value: 'opinion' },
  { label: 'news', value: 'news' },
  { label: 'update', value: 'update' },

  // Industry tags
  { label: 'technology', value: 'technology' },
  { label: 'business', value: 'business' },
  { label: 'entrepreneurship', value: 'entrepreneurship' },
  { label: 'innovation', value: 'innovation' },
  { label: 'startup', value: 'startup' },
  { label: 'funding', value: 'funding' },
  { label: 'investment', value: 'investment' },
  { label: 'research', value: 'research' },
  { label: 'development', value: 'development' },

  // Community tags
  { label: 'community', value: 'community' },
  { label: 'networking', value: 'networking' },
  { label: 'collaboration', value: 'collaboration' },
  { label: 'partnership', value: 'partnership' },
  { label: 'mentorship', value: 'mentorship' },
  { label: 'resources', value: 'resources' },
  { label: 'opportunities', value: 'opportunities' },

  // Sentiment tags
  { label: 'inspiration', value: 'inspiration' },
  { label: 'motivation', value: 'motivation' },
  { label: 'success-story', value: 'success-story' },
  { label: 'challenge', value: 'challenge' },
  { label: 'learning', value: 'learning' }
];

// Filtered predefined tags
const predefinedTagsFiltered = ref(predefinedTagOptions);

// Methods
function handleImageUpload(file) {
  if (!file) return;

  // Create a data URL for preview
  const reader = new FileReader();
  reader.onload = (e) => {
    formData.value.image = e.target.result;
  };
  reader.readAsDataURL(file);

  // Note: The actual upload to Supabase Storage will happen in the posts store
  // when the post is created. We just store the data URL here for preview.
}

function removeImage() {
  formData.value.image = null;
  imageFile.value = null;
}

function onRejected(rejectedEntries) {
  // Display notification for rejected files
  rejectedEntries.forEach(entry => {
    let message = '';
    if (entry.failedPropValidation === 'accept') {
      message = 'Please upload only image files (JPG, PNG, GIF)';
    } else if (entry.failedPropValidation === 'max-file-size') {
      message = 'File is too large. Maximum size is 5MB';
    } else {
      message = 'File upload failed';
    }

    $q.notify({
      type: 'negative',
      message
    });
  });
}

// Filter predefined tags based on user input
function filterTags(val, update) {
  if (val === '') {
    update(() => {
      // Show all options when no search term
      predefinedTagsFiltered.value = predefinedTagOptions;
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    // Filter options based on search term
    predefinedTagsFiltered.value = predefinedTagOptions.filter(
      v => v.label.toLowerCase().indexOf(needle) > -1
    );
  });
}

// Add a predefined tag to the list
function addPredefinedTag(tag) {
  if (!tag) return;

  // Extract the tag value if it's an object
  let tagValue;
  if (typeof tag === 'object' && tag !== null) {
    // Use the value property if available, otherwise use label
    tagValue = tag.value || tag.label;
  } else {
    tagValue = tag;
  }

  // Add the tag if it's not already in the list
  if (tagValue && !formData.value.tags.includes(tagValue)) {
    formData.value.tags.push(tagValue);
  }

  // Clear the selection for next use
  selectedPredefinedTag.value = null;
}

// Process manually entered tags
function processTags() {
  if (!tagsInput.value) return;

  // Split by comma and trim whitespace
  const newTags = tagsInput.value.split(',')
    .map(tag => tag.trim())
    .filter(tag => tag && !formData.value.tags.includes(tag)); // Only add tags that don't already exist

  // Add new tags to the existing tags
  if (newTags.length > 0) {
    formData.value.tags = [...formData.value.tags, ...newTags];
  }

  // Clear the input
  tagsInput.value = '';
}

// Remove a tag from the list
function removeTag(index) {
  formData.value.tags.splice(index, 1);
}

async function handleSubmit() {
  localLoading.value = true;

  try {
    // Process tags one more time before submission
    processTags();

    // Automatically add main category and subcategory as tags if they're not already included
    const mainCategory = formData.value.postType?.toLowerCase() || 'general';
    const subCategory = formData.value.subType?.toLowerCase() || '';

    // Add main category as a tag if not already included
    if (mainCategory && !formData.value.tags.includes(mainCategory)) {
      formData.value.tags.push(mainCategory);
    }

    // Add subcategory as a tag if it exists and is not already included
    if (subCategory && !formData.value.tags.includes(subCategory)) {
      formData.value.tags.push(subCategory);
    }

    // If still no tags were added, try to extract some from the content
    if (!formData.value.tags || formData.value.tags.length === 0) {
      // Look for common keywords in the content
      const content = formData.value.content.toLowerCase();
      const possibleTags = [];

      // Check for common topics in the content
      predefinedTagOptions.forEach(tag => {
        if (content.includes(tag.value)) {
          possibleTags.push(tag.value);
        }
      });

      // Use up to 3 extracted tags
      if (possibleTags.length > 0) {
        formData.value.tags = possibleTags.slice(0, 3);
      } else {
        // Default to 'general' tag if no tags could be extracted
        formData.value.tags = ['general'];
      }
    }

    // Process tags to ensure they're all strings (not objects)
    const processedTags = (formData.value.tags || []).map(tag => {
      if (typeof tag === 'object' && tag !== null) {
        return tag.label || tag.value || String(tag);
      }
      return String(tag);
    });

    // Create post object with postType and subType
    const post = {
      ...formData.value,
      postType: 'general',
      subType: 'general', // Adding subType to match the expected structure
      content: formData.value.content,
      featuredImage: formData.value.image, // Map image to featuredImage for consistency
      author: 'Current User', // This would come from the auth store
      authorAvatar: 'https://cdn.quasar.dev/img/avatar.png', // This would come from the auth store
      createdAt: new Date().toISOString(),
      likesCount: 0,
      commentsCount: 0,
      status: 'published', // Must be lowercase to match database constraint
      tags: processedTags // Use the processed tags
    };

    // Emit the post data to parent component
    emit('submit', post);
  } catch (error) {
    console.error('Error creating post:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to create post. Please try again.'
    });
  } finally {
    // Add a small delay to show the loading state
    setTimeout(() => {
      localLoading.value = false;
    }, 500);
  }
}
</script>

<style scoped>
.image-preview {
  position: relative;
  display: inline-block;
}
</style>
