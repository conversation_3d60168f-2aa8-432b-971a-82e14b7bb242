<template>
  <div class="mobile-filters-container">
    <!-- Tab Navigation - Hidden on mobile -->
    <!-- <div class="mobile-tabs">
      <q-btn-toggle
        v-model="selectedTab"
        toggle-color="primary"
        :options="tabOptions"
        @update:model-value="handleTabChange"
        class="tab-toggle"
        dense
        unelevated
      />
    </div> -->

    <!-- Filter Actions -->
    <div class="filter-actions">
      <!-- AI Filter Trigger (Far Left) -->
      <q-btn
        flat
        dense
        round
        icon="psychology"
        color="primary"
        @click="triggerAIChat"
        class="ai-filter-btn"
      >
        <q-tooltip>AI Chat</q-tooltip>
      </q-btn>

      <!-- Tab Dropdown (Center) - Desktop Style -->
      <q-select
        v-model="selectedTab"
        :options="tabOptions"
        filled
        dense
        emit-value
        map-options
        @update:model-value="handleTabChange"
        class="mobile-tab-select"
        dropdown-icon="expand_more"
      >
        <template v-slot:prepend>
          <q-icon :name="getTabIcon(selectedTab)" class="nav-icon" />
        </template>
      </q-select>

      <!-- Filter Menu (Far Right) -->
      <q-btn
        flat
        dense
        round
        icon="tune"
        @click="openFilterMenu"
        class="filter-btn"
      >
        <q-badge
          v-if="activeFiltersCount > 0"
          color="red"
          floating
          rounded
          :label="activeFiltersCount"
        />
        <q-tooltip>Filters</q-tooltip>
      </q-btn>
    </div>



    <!-- Filter Menu Dialog -->
    <q-dialog v-model="showFilterMenu" position="top">
      <q-card class="filter-menu-card">
        <q-card-section class="filter-header">
          <div class="text-h6">Filters</div>
          <q-btn flat round dense icon="close" v-close-popup />
        </q-card-section>
        
        <q-card-section>
          <!-- Main Category Selection -->
          <div class="filter-group">
            <div class="filter-group-title">Main Category</div>
            <q-select
              v-model="selectedMainCategory"
              :options="mainCategoryOptions"
              outlined
              dense
              emit-value
              map-options
              @update:model-value="handleMainCategoryChange"
              clearable
            />
          </div>

          <!-- Tag Options (Dynamic based on main category) -->
          <div v-if="selectedMainCategory && availableTagOptions.length > 0" class="filter-group">
            <div class="filter-group-title">Tags</div>
            <q-option-group
              v-model="selectedTags"
              :options="availableTagOptions"
              type="checkbox"
              inline
              @update:model-value="handleFilterChange"
            />
          </div>

          <!-- Date Range (positioned at bottom) -->
          <div class="filter-group">
            <div class="filter-group-title">Date Range</div>
            <q-select
              v-model="selectedDateRange"
              :options="dateRangeOptions"
              outlined
              dense
              @update:model-value="handleFilterChange"
            />
          </div>

          <!-- Tab-specific filters -->
          <!-- Profile Type (for profiles tab) -->
          <div v-if="activeTab === 'profiles'" class="filter-group">
            <div class="filter-group-title">Profile Type</div>
            <q-option-group
              v-model="selectedProfileTypes"
              :options="profileTypeOptions"
              type="checkbox"
              inline
              @update:model-value="handleFilterChange"
            />
          </div>

          <!-- Event Type (for events tab) -->
          <div v-if="activeTab === 'events'" class="filter-group">
            <div class="filter-group-title">Event Type</div>
            <q-option-group
              v-model="selectedEventTypes"
              :options="eventTypeOptions"
              type="checkbox"
              inline
              @update:model-value="handleFilterChange"
            />
          </div>

          <!-- Blog Category (for blog tab) -->
          <div v-if="activeTab === 'blog'" class="filter-group">
            <div class="filter-group-title">Blog Category</div>
            <q-option-group
              v-model="selectedBlogCategories"
              :options="blogCategoryOptions"
              type="checkbox"
              inline
              @update:model-value="handleFilterChange"
            />
          </div>

          <!-- Marketplace Category (for marketplace tab) -->
          <div v-if="activeTab === 'marketplace'" class="filter-group">
            <div class="filter-group-title">Product Category</div>
            <q-option-group
              v-model="selectedMarketplaceCategories"
              :options="marketplaceCategoryOptions"
              type="checkbox"
              inline
              @update:model-value="handleFilterChange"
            />
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Clear All" @click="clearAllFilters" />
          <q-btn unelevated color="primary" label="Search & View Results" icon="search" @click="handleFilterResults" />
        </q-card-actions>

        <!-- Results Section (shown when results are available) -->
        <q-card-section v-if="showResults" class="results-section">
          <q-separator class="q-mb-md" />
          <div class="text-h6 q-mb-md">
            Search Results ({{ resultCount }})
          </div>

          <q-scroll-area style="height: 400px;">
            <div class="results-container">
              <!-- Feed Results -->
              <div v-if="activeTab === 'feed' && posts.length > 0" class="results-list">
                <PostCard
                  v-for="post in posts"
                  :key="post.id"
                  :post="post"
                  class="q-mb-md"
                  @like="$emit('like', $event)"
                  @comment="$emit('comment', $event)"
                  @share="$emit('share', $event)"
                />
              </div>

              <!-- Events Results -->
              <div v-else-if="activeTab === 'events' && events.length > 0" class="results-list">
                <EventCard
                  v-for="event in events"
                  :key="event.id"
                  :event="event"
                  class="q-mb-md"
                  @share="$emit('share', $event)"
                />
              </div>

              <!-- Profiles Results -->
              <div v-else-if="activeTab === 'profiles' && profiles.length > 0" class="results-list">
                <ProfileCard
                  v-for="profile in profiles"
                  :key="profile.id"
                  :profile="profile"
                  class="q-mb-md"
                  @contact="$emit('contact', $event)"
                  @collaborate="$emit('collaborate', $event)"
                />
              </div>

              <!-- Blog Results -->
              <div v-else-if="activeTab === 'blog' && articles.length > 0" class="results-list">
                <BlogCard
                  v-for="article in articles"
                  :key="article.id"
                  :article="article"
                  class="q-mb-md"
                  @like="$emit('like', $event)"
                  @share="$emit('share', $event)"
                />
              </div>

              <!-- Marketplace Results -->
              <div v-else-if="activeTab === 'marketplace' && marketplace.length > 0" class="results-list">
                <MarketplaceCard
                  v-for="item in marketplace"
                  :key="item.id"
                  :item="item"
                  class="q-mb-md"
                  @contact="$emit('contact', $event)"
                  @share="$emit('share', $event)"
                />
              </div>

              <!-- No Results -->
              <div v-else class="no-results text-center q-pa-lg">
                <q-icon name="search_off" size="3rem" color="grey-4" />
                <div class="text-h6 text-grey-6 q-mt-md">No results found</div>
                <div class="text-body2 text-grey-5 q-mt-sm">
                  Try adjusting your filters
                </div>
              </div>

              <!-- Load More Button -->
              <div v-if="hasMore && resultCount > 0" class="text-center q-mt-md">
                <q-btn
                  unelevated
                  color="primary"
                  label="Load More"
                  @click="$emit('load-more')"
                  :loading="loading"
                />
              </div>
            </div>
          </q-scroll-area>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useGlobalServicesStore } from '../../stores/globalServices'
import PostCard from '../feed/cards/PostCard.vue'
import EventCard from '../feed/cards/EventCard.vue'
import ProfileCard from '../feed/cards/ProfileCard.vue'
import BlogCard from '../feed/cards/BlogCard.vue'
import MarketplaceCard from '../feed/cards/MarketplaceCard.vue'
import { filterOptions } from '../../services/filterOptionsService'

const route = useRoute()
const globalServices = useGlobalServicesStore()

// Props
interface Props {
  activeTab: string
  posts?: any[]
  events?: any[]
  profiles?: any[]
  articles?: any[]
  marketplace?: any[]
  loading?: boolean
  hasMore?: boolean
  showResults?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  posts: () => [],
  events: () => [],
  profiles: () => [],
  articles: () => [],
  marketplace: () => [],
  loading: false,
  hasMore: false,
  showResults: false
})

// Emits
const emit = defineEmits<{
  'filter-change': [filters: any]
  'filter-results': [filters: any]
  'tab-change': [tab: string]
}>()

// State
const selectedTab = ref(props.activeTab)
const showFilterMenu = ref(false)
const showResults = ref(false)
const currentSort = ref('newest')
const selectedCategories = ref([])
const selectedMainCategory = ref('')
const selectedTags = ref([])
const selectedDateRange = ref('all')
const selectedProfileTypes = ref([])
const selectedEventTypes = ref([])
const selectedBlogCategories = ref([])
const selectedMarketplaceCategories = ref([])

// Options
const tabOptions = [
  { label: 'Community Feed', value: 'feed' },
  { label: 'Profiles Directory', value: 'profiles' },
  { label: 'Events', value: 'events' },
  { label: 'Blog', value: 'blog' },
  { label: 'Marketplace', value: 'marketplace' }
]

const sortOptions = [
  { label: 'Newest First', value: 'newest', icon: 'schedule' },
  { label: 'Oldest First', value: 'oldest', icon: 'history' },
  { label: 'Most Popular', value: 'popular', icon: 'trending_up' },
  { label: 'Most Relevant', value: 'relevant', icon: 'star' }
]

const categoryOptions = [
  { label: 'General', value: 'general' },
  { label: 'Technology', value: 'technology' },
  { label: 'Business', value: 'business' },
  { label: 'Education', value: 'education' },
  { label: 'Innovation', value: 'innovation' }
]

// Main category options (standardized structure)
const mainCategoryOptions = [
  { label: 'General Posts', value: 'general', icon: 'forum' },
  { label: 'Technology', value: 'technology', icon: 'computer' },
  { label: 'Business', value: 'business', icon: 'business' },
  { label: 'Education', value: 'education', icon: 'school' },
  { label: 'Innovation', value: 'innovation', icon: 'lightbulb' }
]

// Tag options based on main category
const tagOptionsMap = {
  general: [
    { label: 'Discussion', value: 'discussion' },
    { label: 'Question', value: 'question' },
    { label: 'Announcement', value: 'announcement' }
  ],
  technology: [
    { label: 'AI/ML', value: 'ai-ml' },
    { label: 'Web Development', value: 'web-dev' },
    { label: 'Mobile Apps', value: 'mobile' },
    { label: 'Data Science', value: 'data-science' }
  ],
  business: [
    { label: 'Startup', value: 'startup' },
    { label: 'Funding', value: 'funding' },
    { label: 'Marketing', value: 'marketing' },
    { label: 'Strategy', value: 'strategy' }
  ],
  education: [
    { label: 'Course', value: 'course' },
    { label: 'Workshop', value: 'workshop' },
    { label: 'Tutorial', value: 'tutorial' },
    { label: 'Research', value: 'research' }
  ],
  innovation: [
    { label: 'Product', value: 'product' },
    { label: 'Process', value: 'process' },
    { label: 'Idea', value: 'idea' },
    { label: 'Patent', value: 'patent' }
  ]
}

const dateRangeOptions = [
  { label: 'All Time', value: 'all' },
  { label: 'Today', value: 'today' },
  { label: 'This Week', value: 'week' },
  { label: 'This Month', value: 'month' },
  { label: 'This Year', value: 'year' }
]

const profileTypeOptions = filterOptions.profileTypeOptions

const eventTypeOptions = filterOptions.eventTypeOptions

const blogCategoryOptions = filterOptions.blogCategoryOptions

const marketplaceCategoryOptions = filterOptions.listingTypeOptions

// Computed result count
const resultCount = computed(() => {
  switch (props.activeTab) {
    case 'feed':
      return props.posts.length
    case 'events':
      return props.events.length
    case 'profiles':
      return props.profiles.length
    case 'blog':
      return props.articles.length
    case 'marketplace':
      return props.marketplace.length
    default:
      return 0
  }
});

const aiSuggestions = [
  'Show trending posts',
  'Find mentors in tech',
  'Upcoming events',
  'Latest innovations',
  'Popular discussions'
]

// Computed
const availableTagOptions = computed(() => {
  if (!selectedMainCategory.value || !tagOptionsMap[selectedMainCategory.value]) {
    return []
  }
  return tagOptionsMap[selectedMainCategory.value]
})

const activeFiltersCount = computed(() => {
  let count = 0
  if (selectedCategories.value.length > 0) count++
  if (selectedMainCategory.value) count++
  if (selectedTags.value.length > 0) count++
  if (selectedDateRange.value !== 'all') count++
  if (selectedProfileTypes.value.length > 0) count++
  if (selectedEventTypes.value.length > 0) count++
  if (selectedBlogCategories.value.length > 0) count++
  if (selectedMarketplaceCategories.value.length > 0) count++
  return count
})

// Methods
function handleTabChange(tab: string) {
  emit('tab-change', tab)
}

async function triggerAIChat() {
  try {
    // Trigger AI chat interface using the same method as other AI buttons
    await globalServices.aiChatTriggerService.triggerChat('ai_search', `community-mobile-${props.activeTab}`)
  } catch (error) {
    console.error('Error triggering AI chat:', error)
  }
}

function openSortMenu() {
  // Menu is handled by q-menu
}

function openFilterMenu() {
  showFilterMenu.value = true
}



function handleSortChange(sortValue: string) {
  currentSort.value = sortValue
  handleFilterChange()
}

function handleMainCategoryChange() {
  // Clear tags when main category changes
  selectedTags.value = []
  handleFilterChange()
}

function handleFilterChange() {
  const filters = {
    sort: currentSort.value,
    categories: selectedCategories.value,
    mainCategory: selectedMainCategory.value,
    tags: selectedTags.value,
    dateRange: selectedDateRange.value,
    profileTypes: selectedProfileTypes.value,
    eventTypes: selectedEventTypes.value,
    blogCategories: selectedBlogCategories.value,
    marketplaceCategories: selectedMarketplaceCategories.value,
    tab: selectedTab.value
  }
  emit('filter-change', filters)
}

function handleFilterResults() {
  const filters = {
    sort: currentSort.value,
    categories: selectedCategories.value,
    mainCategory: selectedMainCategory.value,
    tags: selectedTags.value,
    dateRange: selectedDateRange.value,
    profileTypes: selectedProfileTypes.value,
    eventTypes: selectedEventTypes.value,
    blogCategories: selectedBlogCategories.value,
    marketplaceCategories: selectedMarketplaceCategories.value,
    tab: selectedTab.value
  }

  // Emit filter-results to trigger database search
  emit('filter-results', filters)

  // Listen for results to be loaded
  const handleResultsLoaded = (event: CustomEvent) => {
    if (event.detail.tab === props.activeTab) {
      // Update local data with results
      Object.assign(props, event.detail.data)
      showResults.value = true
      showFilterMenu.value = true
    }
  }

  // Listen for results loaded event
  window.addEventListener('filter-results-loaded', handleResultsLoaded, { once: true })
}

function clearAllFilters() {
  selectedCategories.value = []
  selectedDateRange.value = 'all'
  selectedProfileTypes.value = []
  selectedEventTypes.value = []
  selectedBlogCategories.value = []
  selectedMarketplaceCategories.value = []
  currentSort.value = 'newest'
  handleFilterChange()
}

function getTabIcon(tab: string) {
  const iconMap: Record<string, string> = {
    feed: 'dynamic_feed',
    profiles: 'people',
    events: 'event',
    blog: 'article',
    marketplace: 'storefront'
  }
  return iconMap[tab] || 'tab'
}

// Initialize filters from URL query parameters
function initializeFiltersFromQuery() {
  const query = route.query

  // Handle profile types from URL
  if (query.profileTypes && props.activeTab === 'profiles') {
    const profileTypes = typeof query.profileTypes === 'string'
      ? query.profileTypes.split(',')
      : query.profileTypes

    selectedProfileTypes.value = profileTypes
    console.log('📱 Mobile: Initialized profile filters from URL:', profileTypes)
  }
}

// Initialize on component setup
initializeFiltersFromQuery()

// Watch for prop changes
watch(() => props.activeTab, (newTab) => {
  selectedTab.value = newTab
})

// Watch for query parameter changes
watch(() => route.query, () => {
  initializeFiltersFromQuery()
}, { deep: true })

// Watch for tab changes to clear filters
watch(() => props.activeTab, (newTab, oldTab) => {
  if (oldTab && newTab !== oldTab) {
    console.log(`Mobile filters: Tab changed from ${oldTab} to ${newTab}, clearing filters`)
    // Clear all filter states
    selectedCategories.value = []
    selectedMainCategory.value = ''
    selectedTags.value = []
    selectedDateRange.value = 'all'
    selectedProfileTypes.value = []
    selectedEventTypes.value = []
    selectedBlogCategories.value = []
    selectedMarketplaceCategories.value = []
    currentSort.value = 'newest'
  }
  selectedTab.value = newTab
})
</script>

<style scoped>
.mobile-filters-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.mobile-tabs {
  flex: 1;
  overflow-x: auto;
  
  .tab-toggle {
    :deep(.q-btn-toggle) {
      border-radius: 20px;
    }
    
    :deep(.q-btn) {
      font-size: 12px;
      padding: 6px 12px;
      min-width: auto;
    }
  }
}

.filter-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.ai-filter-btn,
.filter-btn {
  color: #6b7280;

  &:hover {
    color: #6366f1;
    background: rgba(99, 102, 241, 0.1);
  }
}

/* AI button stays on the left */
.ai-filter-btn {
  order: 1;
  flex-shrink: 0;
}

/* Tab dropdown in the center */
.mobile-tab-select {
  order: 2;
  flex: 1;
  max-width: 200px;
  margin: 0 8px;

  :deep(.q-field__control) {
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    min-height: 36px;
  }

  :deep(.q-field__native) {
    font-size: 14px;
    font-weight: 500;
  }
}

.nav-icon {
  color: #0D8A3E;
}

/* Filter button on the right */
.filter-btn {
  order: 3;
  flex-shrink: 0;
}

.ai-filter-card {
  width: 100%;
  max-width: 400px;
  margin: 16px;
}

.ai-filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
}

.ai-suggestions {
  margin-top: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-chip {
  font-size: 11px;
  height: 24px;
}

.filter-menu-card {
  width: 100%;
  max-height: 70vh;
  margin: 0;
  border-radius: 16px 16px 0 0;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.filter-group {
  margin-bottom: 20px;
}

.filter-group-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .mobile-filters-container {
    gap: 8px;
  }
  
  .filter-actions {
    gap: 2px;
  }
  
  .ai-filter-btn,
  .sort-btn,
  .filter-btn {
    padding: 6px;
  }
}
</style>
