/**
 * AI Chat Service - Clean Implementation
 *
 * Handles all AI chat functionality with proper error handling
 * and response processing. Fixed browser compatibility issues.
 */

import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/stores/auth'
import { aiProfileAnalyzer, type ProfileAnalysis } from './aiProfileAnalyzer'
import { aiEnhancedContentMatching, type ContentMatchRequest } from './aiEnhancedContentMatching'
import { aiSmartRecommendationEngine, type RecommendationRequest } from './aiSmartRecommendationEngine'

// Types
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp?: Date
  id?: string
}

export interface UserContext {
  is_authenticated: boolean
  user_id?: string
  profile_type?: string
  current_page?: string
  profile_data?: any
  // Enhanced context fields
  profile_completion?: number
  profile_state?: string
  has_multiple_profiles?: boolean
  interests?: string[]
  recent_activity?: string[]
  journey_stage?: string
  platform_familiarity?: 'new' | 'intermediate' | 'experienced'
  onboarding_completed?: boolean
  last_ai_interaction?: string
  // Profile analysis fields (optional)
  profile_analysis?: ProfileAnalysis
  completion_stage?: string
  missing_critical_fields?: string[]
  profile_strengths?: string[]
  next_steps?: string[]
  milestone_achieved?: string
  // Real-time platform awareness
  platform_state?: any
  session_duration?: number
  current_time?: string
  timezone?: string
}

export interface AIChatRequest {
  message: string
  conversation_history?: ChatMessage[]
  user_context: UserContext
  rag_enabled?: boolean
  max_context_items?: number
}

export interface AIActionButton {
  id: string
  label: string
  icon?: string
  color?: string
  action_type: 'navigation' | 'dialog' | 'trigger' | 'external'
  action_data: {
    route?: string
    dialog?: string
    trigger_key?: string
    message?: string
    url?: string
    params?: Record<string, any>
  }
  tooltip?: string
  requires_auth?: boolean
}

export interface AIChatResponse {
  success: boolean
  message?: string
  action_buttons?: AIActionButton[]
  rag_context_used?: any[]
  processing_time_ms?: number
  query_route?: string
  error?: string
}

class AIChatService {
  private baseUrl: string
  private sessionStartTime: number

  constructor() {
    this.baseUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1`
    this.sessionStartTime = Date.now()
  }

  /**
   * Get user context for AI requests
   */
  private async getUserContext(currentPage?: string, includeAnalysis: boolean = false): Promise<UserContext> {
    const authStore = useAuthStore()
    const user = authStore.user

    // Get profile data from profile store for more comprehensive context
    let profile = null
    let profileCompletion = 0
    let profileState = null
    let hasMultipleProfiles = false
    let allUserProfiles = []

    try {
      // Import profile store dynamically to avoid circular dependencies
      const { useProfileStore } = await import('../stores/profileStore')
      const profileStore = useProfileStore()

      profile = profileStore.currentProfile
      profileCompletion = profileStore.profileCompletion || 0
      hasMultipleProfiles = profileStore.userProfiles?.length > 1
      profileState = profile?.profile_state
      allUserProfiles = profileStore.userProfiles || []
    } catch (error) {
      console.warn('Could not access profile store for AI context:', error)
      // Fallback to auth store profile
      profile = authStore.profile
      profileCompletion = profile?.profile_completion || 0
    }

    // Get additional profile details if authenticated
    let enhancedProfileData = null
    if (user?.id && profile) {
      try {
        enhancedProfileData = await this.getEnhancedProfileData(user.id, profile.profile_type)
      } catch (error) {
        console.warn('Could not get enhanced profile data:', error)
      }
    }

    // Get real-time platform state
    const platformState = await this.getPlatformState(user?.id)

    const context: UserContext = {
      is_authenticated: !!user,
      user_id: user?.id,
      profile_type: profile?.profile_type,
      current_page: currentPage || this.getCurrentPage(),
      profile_data: enhancedProfileData || profile,
      // Enhanced context
      profile_completion: profileCompletion,
      profile_state: profileState,
      has_multiple_profiles: hasMultipleProfiles,
      journey_stage: this.determineJourneyStage(profile, profileCompletion),
      platform_familiarity: this.determinePlatformFamiliarity(user),
      onboarding_completed: profileCompletion > 0,
      interests: this.extractUserInterests(enhancedProfileData || profile),
      recent_activity: this.getRecentActivityContext(),
      // Real-time platform awareness
      platform_state: platformState,
      session_duration: this.getSessionDuration(),
      current_time: new Date().toISOString(),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    }

    // Optionally include detailed profile analysis for enhanced AI responses
    if (includeAnalysis && user?.id && profile) {
      try {
        const analysis = await aiProfileAnalyzer.analyzeProfile(user.id, profile)
        context.profile_analysis = analysis
        context.completion_stage = analysis.completion_stage
        context.missing_critical_fields = analysis.missing_critical_fields
        context.profile_strengths = analysis.strengths
        context.next_steps = analysis.next_steps
        context.milestone_achieved = analysis.milestone_achieved
      } catch (error) {
        console.warn('Could not get profile analysis for AI context:', error)
      }
    }

    return context
  }

  /**
   * Get real-time platform state for enhanced context awareness
   */
  private async getPlatformState(userId?: string): Promise<any> {
    try {
      const state: any = {
        active_users_count: 0,
        recent_posts_count: 0,
        trending_topics: [],
        user_notifications: 0,
        user_connections: 0,
        user_activity_score: 0
      }

      if (userId) {
        // Get user-specific platform state
        const [notifications, connections, activity] = await Promise.allSettled([
          this.getUserNotificationCount(userId),
          this.getUserConnectionCount(userId),
          this.getUserActivityScore(userId)
        ])

        if (notifications.status === 'fulfilled') state.user_notifications = notifications.value
        if (connections.status === 'fulfilled') state.user_connections = connections.value
        if (activity.status === 'fulfilled') state.user_activity_score = activity.value
      }

      // Get general platform metrics (simplified for performance)
      state.platform_health = 'active'
      state.peak_hours = this.isPeakHours()

      return state
    } catch (error) {
      console.warn('Error getting platform state:', error)
      return { platform_health: 'unknown' }
    }
  }

  /**
   * Get session duration in minutes
   */
  private getSessionDuration(): number {
    return Math.floor((Date.now() - this.sessionStartTime) / (1000 * 60))
  }

  /**
   * Check if current time is during peak platform hours
   */
  private isPeakHours(): boolean {
    const hour = new Date().getHours()
    // Peak hours: 9-11 AM and 2-5 PM local time
    return (hour >= 9 && hour <= 11) || (hour >= 14 && hour <= 17)
  }

  /**
   * Get user notification count
   */
  private async getUserNotificationCount(userId: string): Promise<number> {
    try {
      const { count } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('read', false)

      return count || 0
    } catch (error) {
      return 0
    }
  }

  /**
   * Get user connection count
   */
  private async getUserConnectionCount(userId: string): Promise<number> {
    try {
      const { count } = await supabase
        .from('connections')
        .select('*', { count: 'exact', head: true })
        .or(`user_id.eq.${userId},connected_user_id.eq.${userId}`)
        .eq('status', 'accepted')

      return count || 0
    } catch (error) {
      return 0
    }
  }

  /**
   * Get user activity score (simplified)
   */
  private async getUserActivityScore(userId: string): Promise<number> {
    try {
      // Simple activity score based on recent actions
      const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()

      const { count } = await supabase
        .from('user_activities')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .gte('created_at', oneWeekAgo)

      return Math.min((count || 0) * 10, 100) // Scale to 0-100
    } catch (error) {
      return 0
    }
  }

  /**
   * Get enhanced profile data including specialized profile information
   */
  private async getEnhancedProfileData(userId: string, profileType?: string): Promise<any> {
    if (!profileType) return null

    try {
      const { data, error } = await supabase
        .from('personal_details')
        .select(`
          *,
          innovator_profiles(*),
          investor_profiles(*),
          mentor_profiles(*),
          professional_profiles(*),
          industry_expert_profiles(*),
          academic_student_profiles(*),
          academic_institution_profiles(*),
          organisation_profiles(*)
        `)
        .eq('user_id', userId)
        .single()

      if (error) {
        console.warn('Error fetching enhanced profile data:', error)
        return null
      }

      return data
    } catch (error) {
      console.warn('Error in getEnhancedProfileData:', error)
      return null
    }
  }

  /**
   * Get current page from URL
   */
  private getCurrentPage(): string {
    const path = window.location.pathname
    if (path === '/') return 'home'
    if (path.includes('/dashboard')) return 'dashboard'
    if (path.includes('/virtual-community')) return 'virtual-community'
    if (path.includes('/about')) return 'about'
    return 'unknown'
  }

  /**
   * Determine user's journey stage based on profile data
   */
  private determineJourneyStage(profile: any, completion: number): string {
    if (!profile) return 'new_user'

    if (completion === 0) return 'just_signed_up'
    if (completion < 30) return 'getting_started'
    if (completion < 70) return 'building_profile'
    if (completion < 100) return 'almost_complete'

    // Check for activity indicators
    if (profile.profile_state === 'PUBLISHED') return 'active_user'

    return 'complete_profile'
  }

  /**
   * Determine platform familiarity based on user data
   */
  private determinePlatformFamiliarity(user: any): 'new' | 'intermediate' | 'experienced' {
    if (!user) return 'new'

    const accountAge = user.created_at ?
      (Date.now() - new Date(user.created_at).getTime()) / (1000 * 60 * 60 * 24) : 0

    if (accountAge < 7) return 'new'
    if (accountAge < 30) return 'intermediate'
    return 'experienced'
  }

  /**
   * Extract user interests from profile data
   */
  private extractUserInterests(profile: any): string[] {
    if (!profile) return []

    const interests: string[] = []

    // Extract from various profile fields based on profile type
    if (profile.profile_type === 'innovator') {
      if (profile.innovation_area) interests.push(profile.innovation_area)
      if (profile.industry_focus) interests.push(...(Array.isArray(profile.industry_focus) ? profile.industry_focus : [profile.industry_focus]))
    }

    if (profile.profile_type === 'investor') {
      if (profile.investment_focus) interests.push(...(Array.isArray(profile.investment_focus) ? profile.investment_focus : [profile.investment_focus]))
      if (profile.industry_preferences) interests.push(...(Array.isArray(profile.industry_preferences) ? profile.industry_preferences : [profile.industry_preferences]))
    }

    if (profile.profile_type === 'mentor') {
      if (profile.expertise_areas) interests.push(...(Array.isArray(profile.expertise_areas) ? profile.expertise_areas : [profile.expertise_areas]))
      if (profile.industry_experience) interests.push(...(Array.isArray(profile.industry_experience) ? profile.industry_experience : [profile.industry_experience]))
    }

    // Add bio keywords if available
    if (profile.bio) {
      const bioKeywords = this.extractKeywordsFromText(profile.bio)
      interests.push(...bioKeywords)
    }

    return [...new Set(interests)].filter(Boolean).slice(0, 10) // Dedupe and limit
  }

  /**
   * Extract keywords from text for interest detection
   */
  private extractKeywordsFromText(text: string): string[] {
    if (!text) return []

    // Simple keyword extraction - could be enhanced with NLP
    const techKeywords = ['AI', 'blockchain', 'fintech', 'healthtech', 'edtech', 'sustainability', 'IoT', 'machine learning', 'data science', 'cybersecurity']
    const industryKeywords = ['healthcare', 'finance', 'education', 'agriculture', 'energy', 'transportation', 'retail', 'manufacturing']

    const allKeywords = [...techKeywords, ...industryKeywords]
    const foundKeywords = allKeywords.filter(keyword =>
      text.toLowerCase().includes(keyword.toLowerCase())
    )

    return foundKeywords
  }

  /**
   * Get recent activity context for AI
   */
  private getRecentActivityContext(): string[] {
    // This could be enhanced to pull from activity tracking service
    // For now, return basic context based on current page
    const currentPage = this.getCurrentPage()
    const activities: string[] = []

    if (currentPage === 'dashboard') activities.push('viewing_dashboard')
    if (currentPage === 'virtual-community') activities.push('browsing_community')
    if (currentPage === 'home') activities.push('on_landing_page')

    return activities
  }

  /**
   * Send message to AI and get response with retry logic
   */
  async sendMessage(
    message: string,
    conversationHistory: ChatMessage[] = [],
    currentPage?: string
  ): Promise<AIChatResponse> {
    const maxRetries = 3;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🤖 Sending AI message (attempt ${attempt}/${maxRetries}):`, {
          message: message.substring(0, 50) + '...',
          currentPage
        });

        const userContext = await this.getUserContext(currentPage, true);

        const requestBody: AIChatRequest = {
          message: message.trim(),
          conversation_history: conversationHistory.slice(-6), // Keep last 6 messages
          user_context: userContext,
          rag_enabled: true,
          max_context_items: 8
        };

      console.log('📤 Request payload:', {
        messageLength: requestBody.message.length,
        historyLength: requestBody.conversation_history?.length || 0,
        userAuthenticated: userContext.is_authenticated,
        currentPage: userContext.current_page
      })

      console.log('🔧 Environment check:', {
        supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
        hasAnonKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
        anonKeyLength: import.meta.env.VITE_SUPABASE_ANON_KEY?.length || 0,
        fullUrl: `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/ai-chat`
      })

      // Get the current session token for authenticated requests
      const { data: { session } } = await supabase.auth.getSession()
      const authToken = session?.access_token || import.meta.env.VITE_SUPABASE_ANON_KEY

      console.log('🔐 Authentication check:', {
        hasSession: !!session,
        hasAccessToken: !!session?.access_token,
        tokenLength: session?.access_token?.length || 0,
        usingAnonKey: !session?.access_token
      })

        // Add timeout to prevent hanging
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 45000); // 45 second timeout

        // Call the AI chat Edge Function using proper authentication
        const httpResponse = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/ai-chat`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

      console.log('📥 Raw response status:', httpResponse.status, httpResponse.statusText)

      if (!httpResponse.ok) {
        const errorText = await httpResponse.text()
        console.error('❌ Edge Function error:', httpResponse.status, errorText)
        throw new Error(`AI service error: ${httpResponse.status} - ${errorText}`)
      }

      const data = await httpResponse.json()
      console.log('📥 Response data:', {
        success: data.success,
        hasMessage: !!data.message,
        messageLength: data.message?.length || 0
      })

      // Handle different response formats
      let response: AIChatResponse
      
      if (data.success === false) {
        console.error('❌ AI service returned error:', data.error)
        throw new Error(data.error || 'AI service returned an error')
      }

      if (data.message) {
        // New format: { success: true, message: "...", ... }
        response = data as AIChatResponse
        console.log('✅ AI response received (new format):', {
          messageLength: response.message?.length || 0,
          processingTime: response.processing_time_ms,
          queryRoute: response.query_route
        })
      } else if (data.response) {
        // Legacy format: { response: "...", ... }
        response = {
          success: true,
          message: data.response,
          processing_time_ms: data.processing_time_ms,
          query_route: data.query_route
        }
        console.log('✅ AI response received (legacy format):', {
          messageLength: response.message?.length || 0,
          processingTime: response.processing_time_ms
        })
      } else {
        console.error('❌ Invalid response format:', data)
        throw new Error('Invalid response format from AI service')
      }

      // Validate response content
      if (!response.message || response.message.trim().length === 0) {
        console.error('❌ Empty AI response received')
        throw new Error('AI service returned an empty response')
      }

      // Enhance response with profile-aware recommendations if applicable
      response = await this.enhanceWithProfileRecommendations(
        response,
        userContext,
        message,
        conversationHistory
      )

      // Generate context-aware action buttons if not provided by AI
      if (!response.action_buttons || response.action_buttons.length === 0) {
        response.action_buttons = await this.generateContextualActionButtons(
          response.message,
          userContext,
          conversationHistory
        )
      }

        return response;

      } catch (error: any) {
        lastError = error;
        console.error(`💥 AI Chat Service Error (attempt ${attempt}/${maxRetries}):`, {
          message: error.message,
          stack: error.stack,
          type: typeof error,
          isTimeout: error.name === 'AbortError'
        });

        // If this is the last attempt, don't retry
        if (attempt === maxRetries) {
          break;
        }

        // Determine if we should retry based on error type
        const shouldRetry = this.shouldRetryError(error);
        if (!shouldRetry) {
          console.log('❌ Error is not retryable, stopping attempts');
          break;
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        console.log(`⏳ Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // All retries failed, return structured error response
    console.error('💥 All retry attempts failed');
    return {
      success: false,
      error: this.getRetryErrorMessage(lastError),
      processing_time_ms: 0
    };
  }

  /**
   * Determine if an error should trigger a retry
   */
  private shouldRetryError(error: Error): boolean {
    const retryableErrors = [
      'fetch',
      'network',
      'timeout',
      'AbortError',
      'Failed to fetch',
      'NetworkError',
      'ERR_NETWORK',
      'ERR_INTERNET_DISCONNECTED'
    ];

    return retryableErrors.some(errorType =>
      error.message.toLowerCase().includes(errorType.toLowerCase()) ||
      error.name.toLowerCase().includes(errorType.toLowerCase())
    );
  }

  /**
   * Get user-friendly error message for retry failures
   */
  private getRetryErrorMessage(error: Error | null): string {
    if (!error) return 'Unknown error occurred';

    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      return 'Request timed out. The AI service is taking longer than expected. Please try again.';
    }

    if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
      return 'Network connection issue. Please check your internet connection and try again.';
    }

    if (error.message.includes('500') || error.message.includes('502') || error.message.includes('503')) {
      return 'AI service is temporarily unavailable. Please try again in a moment.';
    }

    return `AI service error: ${error.message}. Please try again.`;
  }



  /**
   * Enhance AI response with profile-aware recommendations
   */
  private async enhanceWithProfileRecommendations(
    response: AIChatResponse,
    userContext: UserContext,
    userMessage: string,
    conversationHistory: ChatMessage[]
  ): Promise<AIChatResponse> {
    try {
      // Check if this is a matchmaking-related query
      const isMatchmakingQuery = this.isMatchmakingQuery(userMessage, conversationHistory)

      if (!isMatchmakingQuery || !userContext.is_authenticated) {
        return response
      }

      console.log('🎯 Enhancing response with profile recommendations')

      // Determine matching strategy based on user message
      const matchingStrategy = this.determineMatchingStrategy(userMessage)

      // Create enhanced content match request
      const matchRequest: ContentMatchRequest = {
        user_id: userContext.user_id!,
        content_type: this.extractContentType(userMessage),
        query: userMessage,
        filters: {
          profile_types: this.extractProfileTypes(userMessage, userContext),
          max_results: 5,
          min_completion: 50
        },
        matching_strategy: matchingStrategy,
        include_explanations: true
      }

      // Get AI-enhanced matches
      const matches = await aiEnhancedContentMatching.findEnhancedMatches(matchRequest)

      if (matches.length > 0) {
        // Enhance the AI response with specific recommendations
        const enhancedMessage = this.formatProfileRecommendations(response.message || '', matches, userContext)

        // Add action buttons for connecting with recommended profiles
        const recommendationActions = this.generateRecommendationActions(matches)

        return {
          ...response,
          message: enhancedMessage,
          action_buttons: [...(response.action_buttons || []), ...recommendationActions]
        }
      }

      return response

    } catch (error) {
      console.error('❌ Error enhancing with profile recommendations:', error)
      return response // Return original response on error
    }
  }

  /**
   * Check if the user message is related to matchmaking/networking
   */
  private isMatchmakingQuery(userMessage: string, conversationHistory: ChatMessage[]): boolean {
    const matchmakingKeywords = [
      'connect', 'find', 'discover', 'match', 'recommend', 'network', 'meet',
      'innovators', 'investors', 'mentors', 'entrepreneurs', 'partners',
      'collaboration', 'opportunities', 'projects', 'investment'
    ]

    const messageLower = userMessage.toLowerCase()
    return matchmakingKeywords.some(keyword => messageLower.includes(keyword))
  }

  /**
   * Extract matching criteria from user message and context
   * NOTE: This method is currently unused - browser compatibility fixed
   */
  private async extractMatchingCriteria(userMessage: string, userContext: UserContext): Promise<any> {
    const criteria: any = {
      exclude_user_id: userContext.user_id,
      limit: 5
    }

    const messageLower = userMessage.toLowerCase()

    // Determine target profile types based on user's profile and message
    if (userContext.profile_type === 'investor') {
      if (messageLower.includes('innovator') || messageLower.includes('entrepreneur')) {
        criteria.profile_type = ['innovator', 'entrepreneur', 'startup']
      }
    } else if (userContext.profile_type === 'innovator') {
      if (messageLower.includes('investor') || messageLower.includes('funding')) {
        criteria.profile_type = ['investor']
      } else if (messageLower.includes('mentor')) {
        criteria.profile_type = ['mentor', 'expert']
      }
    } else if (userContext.profile_type === 'mentor') {
      criteria.profile_type = ['innovator', 'entrepreneur', 'student']
    }

    // If no specific type mentioned, use recommended types
    if (!criteria.profile_type) {
      try {
        const { useProfileMatchingService } = await import('./profileMatchingService')
        const profileMatchingService = useProfileMatchingService()
        criteria.profile_type = profileMatchingService.getRecommendedProfileTypes(userContext.profile_type || 'user')
      } catch (error) {
        console.warn('Could not load profile matching service:', error)
        // Fallback to default profile types
        criteria.profile_type = ['mentor', 'innovator', 'investor']
      }
    }

    return criteria
  }

  /**
   * Format profile recommendations into enhanced AI response
   */
  private formatProfileRecommendations(originalMessage: string, matches: any[], userContext: UserContext): string {
    const topMatches = matches.slice(0, 3) // Show top 3 matches

    let enhancedMessage = originalMessage + '\n\n**🎯 Personalized Recommendations:**\n\n'

    topMatches.forEach((match, index) => {
      const name = match.profile_name || `${match.first_name || ''} ${match.last_name || ''}`.trim() || 'User'
      const score = match.compatibility_score
      const reasons = match.match_reasons.slice(0, 2).join(', ')

      enhancedMessage += `**${index + 1}. ${name}** (${match.profile_type})\n`
      enhancedMessage += `• Compatibility: ${score}% match\n`
      enhancedMessage += `• Why: ${reasons}\n`
      if (match.bio) {
        enhancedMessage += `• About: ${match.bio.substring(0, 100)}${match.bio.length > 100 ? '...' : ''}\n`
      }
      enhancedMessage += '\n'
    })

    enhancedMessage += `Found ${matches.length} total matches. These recommendations are based on your profile, interests, and compatibility analysis.`

    return enhancedMessage
  }

  /**
   * Generate action buttons for profile recommendations
   */
  private generateRecommendationActions(matches: any[]): AIActionButton[] {
    const actions: AIActionButton[] = []

    // Add "View All Matches" action
    actions.push({
      id: 'view-all-matches',
      label: 'View All Matches',
      icon: 'people',
      color: 'primary',
      action_type: 'navigation',
      action_data: { route: '/innovation-community', params: { tab: 'profiles' } },
      tooltip: 'Browse all matching profiles'
    })

    // Add action for top match if available
    if (matches.length > 0) {
      const topMatch = matches[0]
      actions.push({
        id: 'connect-top-match',
        label: `Connect with ${topMatch.profile_name || 'Top Match'}`,
        icon: 'person_add',
        color: 'secondary',
        action_type: 'dialog',
        action_data: {
          dialog: 'message-user',
          params: {
            userId: topMatch.user_id,
            userName: topMatch.profile_name
          }
        },
        tooltip: 'Send a message to this recommended connection',
        requires_auth: true
      })
    }

    return actions
  }

  /**
   * Generate contextual action buttons based on AI response and user context
   */
  private async generateContextualActionButtons(
    aiMessage: string,
    userContext: UserContext,
    conversationHistory: ChatMessage[]
  ): Promise<AIActionButton[]> {
    try {
      const actions: AIActionButton[] = []
      const messageLower = aiMessage.toLowerCase()

      // Authentication-based actions
      if (!userContext.is_authenticated) {
        if (messageLower.includes('sign in') || messageLower.includes('log in') || messageLower.includes('account')) {
          actions.push({
            id: 'auth-signin',
            label: 'Sign In',
            icon: 'login',
            color: 'primary',
            action_type: 'dialog',
            action_data: { dialog: 'auth-signin' },
            tooltip: 'Sign in to access all features'
          })
        }

        if (messageLower.includes('join') || messageLower.includes('register') || messageLower.includes('sign up')) {
          actions.push({
            id: 'auth-signup',
            label: 'Join Platform',
            icon: 'person_add',
            color: 'secondary',
            action_type: 'dialog',
            action_data: { dialog: 'auth-signup' },
            tooltip: 'Create your account'
          })
        }
      } else {
        // Authenticated user actions

        // Profile-related actions
        if (messageLower.includes('profile') || messageLower.includes('complete')) {
          const profileCompletion = userContext.profile_data?.profile_completion || 0
          if (profileCompletion < 80) {
            actions.push({
              id: 'complete-profile',
              label: 'Complete Profile',
              icon: 'person',
              color: 'orange',
              action_type: 'navigation',
              action_data: { route: '/dashboard/profile' },
              tooltip: 'Complete your profile for better visibility',
              requires_auth: true
            })
          }
        }

        // Navigation actions based on content
        if (messageLower.includes('dashboard') && userContext.current_page !== 'dashboard') {
          actions.push({
            id: 'go-dashboard',
            label: 'Go to Dashboard',
            icon: 'dashboard',
            color: 'primary',
            action_type: 'navigation',
            action_data: { route: '/dashboard' },
            tooltip: 'Access your personalized dashboard',
            requires_auth: true
          })
        }

        if (messageLower.includes('community') || messageLower.includes('feed') || messageLower.includes('connect')) {
          actions.push({
            id: 'explore-community',
            label: 'Explore Community',
            icon: 'people',
            color: 'secondary',
            action_type: 'navigation',
            action_data: { route: '/innovation-community', params: { tab: 'feed' } },
            tooltip: 'Discover the innovation community'
          })
        }

        if (messageLower.includes('profile') || messageLower.includes('network') || messageLower.includes('connect')) {
          actions.push({
            id: 'browse-profiles',
            label: 'Browse Profiles',
            icon: 'people',
            color: 'blue',
            action_type: 'navigation',
            action_data: { route: '/innovation-community', params: { tab: 'profiles' } },
            tooltip: 'Browse and connect with other innovators'
          })
        }

        if (messageLower.includes('marketplace') || messageLower.includes('opportunity') || messageLower.includes('collaboration')) {
          actions.push({
            id: 'explore-marketplace',
            label: 'Explore Marketplace',
            icon: 'store',
            color: 'teal',
            action_type: 'navigation',
            action_data: { route: '/innovation-community', params: { tab: 'marketplace' } },
            tooltip: 'Find opportunities and collaborations'
          })
        }

        // Content creation actions
        if (messageLower.includes('post') || messageLower.includes('share') || messageLower.includes('create')) {
          actions.push({
            id: 'create-post',
            label: 'Create Post',
            icon: 'edit',
            color: 'green',
            action_type: 'dialog',
            action_data: { dialog: 'post-creation', params: { type: 'general' } },
            tooltip: 'Share your thoughts with the community',
            requires_auth: true
          })
        }

        // Help and support actions
        if (messageLower.includes('help') || messageLower.includes('support') || messageLower.includes('guide')) {
          actions.push({
            id: 'get-help',
            label: 'Get Help',
            icon: 'help',
            color: 'info',
            action_type: 'navigation',
            action_data: { route: '/dashboard/help' },
            tooltip: 'Access help and support resources',
            requires_auth: true
          })
        }

        // Quick follow-up actions based on conversation context
        if (messageLower.includes('project') || messageLower.includes('innovation')) {
          actions.push({
            id: 'discover-projects',
            label: 'Discover Projects',
            icon: 'lightbulb',
            color: 'amber',
            action_type: 'navigation',
            action_data: { route: '/virtual-community', params: { tab: 'projects' } },
            tooltip: 'Explore innovative projects'
          })
        }

        if (messageLower.includes('event') || messageLower.includes('workshop') || messageLower.includes('meetup')) {
          actions.push({
            id: 'browse-events',
            label: 'Browse Events',
            icon: 'event',
            color: 'purple',
            action_type: 'navigation',
            action_data: { route: '/virtual-community', params: { tab: 'events' } },
            tooltip: 'Find upcoming events and workshops'
          })
        }
      }

      // Add general follow-up quick replies
      this.addQuickFollowUpActions(actions, messageLower, userContext)

      // Remove duplicates based on ID
      const uniqueActions = actions.filter((action, index, self) =>
        index === self.findIndex(a => a.id === action.id)
      )

      console.log('🎯 Generated contextual actions:', uniqueActions.map(a => a.label))
      return uniqueActions.slice(0, 4) // Limit to 4 actions to avoid UI clutter

    } catch (error) {
      console.error('❌ Error generating contextual action buttons:', error)
      return []
    }
  }

  /**
   * Add quick follow-up actions based on conversation context
   */
  private addQuickFollowUpActions(actions: AIActionButton[], messageLower: string, userContext: UserContext): void {
    // Add common follow-up questions as quick replies
    const followUpActions: AIActionButton[] = []

    if (userContext.is_authenticated) {
      // Authenticated user follow-ups
      if (messageLower.includes('recommend') || messageLower.includes('suggest')) {
        followUpActions.push({
          id: 'tell-me-more',
          label: 'Tell me more',
          icon: 'info',
          color: 'grey-7',
          action_type: 'trigger',
          action_data: { message: 'Can you provide more details about this?' },
          tooltip: 'Get more detailed information'
        })
      }

      if (messageLower.includes('connect') || messageLower.includes('network')) {
        followUpActions.push({
          id: 'how-to-connect',
          label: 'How to connect?',
          icon: 'help_outline',
          color: 'grey-7',
          action_type: 'trigger',
          action_data: { message: 'How can I connect with these people?' },
          tooltip: 'Learn about connection process'
        })
      }

      if (messageLower.includes('project') || messageLower.includes('opportunity')) {
        followUpActions.push({
          id: 'find-similar',
          label: 'Find similar',
          icon: 'search',
          color: 'grey-7',
          action_type: 'trigger',
          action_data: { message: 'Can you find similar projects or opportunities?' },
          tooltip: 'Discover similar content'
        })
      }
    } else {
      // Unauthenticated user follow-ups
      followUpActions.push({
        id: 'learn-more',
        label: 'Learn more',
        icon: 'school',
        color: 'grey-7',
        action_type: 'trigger',
        action_data: { message: 'Tell me more about the platform features' },
        tooltip: 'Learn about platform features'
      })
    }

    // Add follow-up actions to main actions array
    actions.push(...followUpActions.slice(0, 2)) // Limit follow-ups to avoid clutter
  }

  /**
   * Test AI service connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.sendMessage('Hello, this is a connection test')
      return response.success && !!response.message
    } catch (error) {
      console.error('AI service connection test failed:', error)
      return false
    }
  }

  /**
   * Determine the best matching strategy based on user message
   */
  private determineMatchingStrategy(userMessage: string): 'semantic' | 'interest_based' | 'hybrid' | 'collaborative' {
    const message = userMessage.toLowerCase()

    // Semantic indicators
    if (message.includes('like') || message.includes('similar') || message.includes('related')) {
      return 'semantic'
    }

    // Interest-based indicators
    if (message.includes('interest') || message.includes('passion') || message.includes('hobby')) {
      return 'interest_based'
    }

    // Collaborative indicators
    if (message.includes('popular') || message.includes('trending') || message.includes('others')) {
      return 'collaborative'
    }

    // Default to hybrid for comprehensive results
    return 'hybrid'
  }

  /**
   * Extract content type from user message
   */
  private extractContentType(userMessage: string): 'profile' | 'post' | 'event' | 'opportunity' {
    const message = userMessage.toLowerCase()

    if (message.includes('post') || message.includes('content') || message.includes('article')) {
      return 'post'
    }

    if (message.includes('event') || message.includes('meetup') || message.includes('conference')) {
      return 'event'
    }

    if (message.includes('opportunity') || message.includes('job') || message.includes('position')) {
      return 'opportunity'
    }

    // Default to profile for networking queries
    return 'profile'
  }

  /**
   * Extract profile types from user message and context
   */
  private extractProfileTypes(userMessage: string, userContext: UserContext): string[] {
    const message = userMessage.toLowerCase()
    const profileTypes: string[] = []

    // Check for specific profile type mentions
    if (message.includes('investor')) profileTypes.push('investor')
    if (message.includes('innovator')) profileTypes.push('innovator')
    if (message.includes('mentor')) profileTypes.push('mentor')
    if (message.includes('professional')) profileTypes.push('professional')
    if (message.includes('student')) profileTypes.push('academic_student')
    if (message.includes('expert')) profileTypes.push('industry_expert')
    if (message.includes('institution') || message.includes('university')) profileTypes.push('academic_institution')

    // If no specific types mentioned, use recommended types based on user's profile
    if (profileTypes.length === 0 && userContext.profile_type) {
      const recommendedTypes: Record<string, string[]> = {
        'innovator': ['investor', 'mentor', 'industry_expert'],
        'investor': ['innovator', 'professional'],
        'mentor': ['innovator', 'academic_student', 'professional'],
        'professional': ['innovator', 'mentor', 'professional'],
        'academic_student': ['mentor', 'industry_expert', 'academic_institution'],
        'industry_expert': ['innovator', 'professional'],
        'academic_institution': ['innovator', 'industry_expert']
      }

      return recommendedTypes[userContext.profile_type] || ['innovator', 'professional']
    }

    return profileTypes
  }

  /**
   * Generate contextual suggestions for quick replies
   */
  async generateContextualSuggestions(
    userContext: UserContext,
    lastAIMessage: string = ''
  ): Promise<string[]> {
    try {
      const suggestions: string[] = []
      const messageLower = lastAIMessage.toLowerCase()

      if (!userContext.is_authenticated) {
        // Unauthenticated user suggestions
        suggestions.push(
          "How do I sign up for ZbInnovation?",
          "What are the benefits of joining?",
          "Tell me about the innovation community",
          "Show me success stories"
        )
      } else {
        // Authenticated user suggestions based on context
        const profileCompletion = userContext.profile_completion || 0

        if (profileCompletion < 50) {
          suggestions.push(
            "How do I complete my profile?",
            "What should I add to my profile?",
            "Help me get started"
          )
        } else {
          // Profile type specific suggestions
          switch (userContext.profile_type) {
            case 'innovator':
              suggestions.push(
                "How do I find investors?",
                "Help me connect with mentors",
                "Show me innovation opportunities"
              )
              break
            case 'investor':
              suggestions.push(
                "Show me promising innovations",
                "How do I evaluate startups?",
                "Find investment opportunities"
              )
              break
            case 'mentor':
              suggestions.push(
                "How do I find mentees?",
                "Show me mentoring opportunities",
                "Help me share my expertise"
              )
              break
            default:
              suggestions.push(
                "Help me find connections",
                "Show me opportunities",
                "What should I do next?"
              )
          }
        }

        // Context-based suggestions from last AI message
        if (messageLower.includes('innovator') || messageLower.includes('innovation')) {
          suggestions.push("Tell me more about innovations")
        }
        if (messageLower.includes('connect') || messageLower.includes('network')) {
          suggestions.push("How do I connect with them?")
        }
        if (messageLower.includes('project') || messageLower.includes('opportunity')) {
          suggestions.push("Find similar projects")
        }
      }

      // Remove duplicates and limit to 4
      const uniqueSuggestions = [...new Set(suggestions)]
      return uniqueSuggestions.slice(0, 4)

    } catch (error) {
      console.error('Error generating contextual suggestions:', error)
      return []
    }
  }

  /**
   * Generate smart recommendations for users asking for recommendations
   */
  async generateSmartRecommendations(
    userContext: UserContext,
    userMessage: string,
    recommendationType: 'profiles' | 'content' | 'opportunities' | 'mixed' = 'mixed'
  ): Promise<AIChatResponse> {
    try {
      if (!userContext.is_authenticated || !userContext.user_id) {
        return {
          success: false,
          message: 'Please log in to get personalized recommendations.',
          action_buttons: [
            {
              id: 'signin',
              label: 'Sign In',
              action_type: 'navigation',
              action_data: { route: '/sign-in' }
            }
          ],
          processing_time_ms: 0
        }
      }

      console.log('🎯 Generating smart recommendations for user')

      // Create recommendation request
      const recommendationRequest: RecommendationRequest = {
        user_id: userContext.user_id,
        recommendation_type: recommendationType,
        context: {
          current_page: userContext.current_page,
          recent_activity: userContext.recent_activity || [],
          search_query: userMessage
        },
        preferences: {
          max_results: 8,
          min_relevance_score: 0.6,
          include_explanations: true,
          diversity_factor: 0.4
        }
      }

      // Get smart recommendations
      const recommendations = await aiSmartRecommendationEngine.generateRecommendations(recommendationRequest)

      if (recommendations.recommendations.length === 0) {
        return {
          success: true,
          message: "I couldn't find specific recommendations right now, but I'd be happy to help you explore the platform! Try completing more of your profile or engaging with content to get better recommendations.",
          action_buttons: [
            {
              id: 'complete-profile',
              label: 'Complete Profile',
              action_type: 'navigation',
              action_data: { route: '/dashboard/profile' },
              requires_auth: true
            },
            {
              id: 'browse-profiles',
              label: 'Browse Profiles',
              action_type: 'navigation',
              action_data: { route: '/virtual-community', params: { tab: 'profiles' } }
            }
          ],
          processing_time_ms: recommendations.processing_time_ms
        }
      }

      // Format recommendations into AI response
      const message = this.formatSmartRecommendationsMessage(recommendations, userContext)
      const actionButtons = this.generateSmartRecommendationActions(recommendations.recommendations)

      return {
        success: true,
        message,
        action_buttons: actionButtons,
        processing_time_ms: recommendations.processing_time_ms
      }

    } catch (error) {
      console.error('Error generating smart recommendations:', error)
      return {
        success: false,
        message: 'I encountered an issue generating recommendations. Please try again.',
        processing_time_ms: 0
      }
    }
  }

  /**
   * Format smart recommendations into a user-friendly message
   */
  private formatSmartRecommendationsMessage(recommendations: any, userContext: UserContext): string {
    const { recommendations: recs, recommendation_strategy, total_found } = recommendations

    let message = `🎯 **Personalized Recommendations for You**\n\n`
    message += `Based on your ${userContext.profile_type || 'profile'} profile and interests, here are ${recs.length} top recommendations:\n\n`

    recs.slice(0, 5).forEach((rec: any, index: number) => {
      message += `**${index + 1}. ${rec.title}**\n`
      message += `${rec.description.substring(0, 120)}...\n`

      if (rec.explanation && rec.explanation.length > 0) {
        message += `*Why this matches: ${rec.explanation[0]}*\n`
      }

      message += `*Relevance: ${Math.round(rec.relevance_score * 100)}%*\n\n`
    })

    if (total_found > recs.length) {
      message += `💡 Found ${total_found} total matches. Use the action buttons below to explore more!\n\n`
    }

    // Add strategy explanation
    const strategyExplanations: Record<string, string> = {
      'vector_similarity_primary': 'Based on semantic similarity to your profile',
      'collaborative_with_onboarding': 'Based on what similar users found valuable',
      'behavior_based_hybrid': 'Based on your recent activity and interests',
      'hybrid_balanced': 'Using a balanced approach across multiple factors'
    }

    if (strategyExplanations[recommendation_strategy]) {
      message += `🔍 *${strategyExplanations[recommendation_strategy]}*`
    }

    return message
  }

  /**
   * Generate action buttons for smart recommendations
   */
  private generateSmartRecommendationActions(recommendations: any[]): AIActionButton[] {
    const actions: AIActionButton[] = []

    // Add actions for top recommendations
    recommendations.slice(0, 3).forEach((rec) => {
      if (rec.type === 'profile') {
        actions.push({
          id: `view-profile-${rec.id}`,
          label: `View ${rec.title}`,
          action_type: 'navigation',
          action_data: { route: `/virtual-community/user/${rec.id}` }
        })
      } else if (rec.type === 'post') {
        actions.push({
          id: `read-post-${rec.id}`,
          label: `Read: ${rec.title.substring(0, 20)}...`,
          action_type: 'navigation',
          action_data: { route: `/virtual-community/post/${rec.id}` }
        })
      }
    })

    // Add general discovery actions
    actions.push(
      {
        id: 'see-all-recommendations',
        label: 'See All Recommendations',
        action_type: 'navigation',
        action_data: { route: '/virtual-community', params: { tab: 'profiles' } }
      },
      {
        id: 'refine-preferences',
        label: 'Refine Preferences',
        action_type: 'navigation',
        action_data: { route: '/dashboard/help' },
        requires_auth: true
      }
    )

    return actions.slice(0, 5) // Limit to 5 actions
  }
}

// Singleton instance
let _aiChatService: AIChatService | null = null

export const getAiChatService = (): AIChatService => {
  if (!_aiChatService) {
    _aiChatService = new AIChatService()
  }
  return _aiChatService
}

// Global test function for debugging
declare global {
  interface Window {
    __testAIChat: () => Promise<void>
  }
}

// Add global test function for debugging
window.__testAIChat = async () => {
  console.log('🧪 Testing AI Chat from global function...')
  try {
    const service = getAiChatService()
    const response = await service.sendMessage('Hello, can you tell me about the ZbInnovation platform?')
    console.log('✅ AI Chat test successful:', response)
  } catch (error) {
    console.error('❌ AI Chat test failed:', error)
  }
}

export default getAiChatService
