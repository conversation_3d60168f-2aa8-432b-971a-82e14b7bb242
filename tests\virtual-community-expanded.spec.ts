import { test, expect } from '@playwright/test'

async function gotoTab(page: any, tab: string) {
  await page.goto(`/virtual-community?tab=${tab}&newUI=1`)
}

test.describe('Virtual Community - New UI expanded checks (resilient)', () => {
  test('Feed shell renders with shelf container (cards optional) and mentorship panel optional', async ({ page }) => {
    await gotoTab(page, 'feed')

    // Shell present
    await expect(page.locator('header.topbar')).toBeVisible()
    await expect(page.locator('.main .center')).toBeVisible()

    // Featured shelf container present even if empty
    const shelf = page.locator('.shelf')
    await expect(shelf).toBeVisible()

    // Cards may be missing depending on data; do not assert card visibility
    // Mentorship panel visibility depends on user role; just ensure query doesn’t throw
    await page.locator('aside.right .panel:has-text("Mentorship")').first().count()
  })

  test('Events tab shows right panel and handles Refresh Featured gracefully', async ({ page }) => {
    await gotoTab(page, 'events')

    await expect(page.locator('aside.right .panel:has-text("Upcoming Events")')).toBeVisible()

    const refreshBtn = page.getByRole('button', { name: 'Refresh Featured' })
    if (await refreshBtn.count()) {
      if (await refreshBtn.first().isVisible()) {
        await refreshBtn.first().click()
      }
    }
  })

  test('Blog tab renders within shell (featured header optional)', async ({ page }) => {
    await gotoTab(page, 'blog')

    await expect(page.locator('header.topbar')).toBeVisible()
    // Either BlogLayout container or center section present; data-dependent headers are optional
    await expect(page.locator('.main .center')).toBeVisible()
  })

  test('Marketplace tab renders grid or empty state without errors', async ({ page }) => {
    await gotoTab(page, 'marketplace')

    const grid = page.locator('.grid')
    const empty = page.locator('.empty')

    // One of them should be present depending on data availability
    await expect(grid.or(empty)).toBeVisible()
  })
})

