<template>
  <q-page class="glass-community-page">
    <!-- Mobile Navigation Drawer -->
    <q-drawer
      v-model="leftDrawerOpen"
      bordered
      :width="250"
      class="mobile-nav-drawer"
      side="left"
      behavior="mobile"
    >
      <q-scroll-area class="fit">
        <q-list padding>
          <q-item-label header class="text-primary text-weight-bold">Navigation</q-item-label>

          <!-- Home Link -->
          <q-item clickable v-ripple @click="goHome" exact>
            <q-item-section avatar>
              <q-icon name="home" color="primary" />
            </q-item-section>
            <q-item-section>Home</q-item-section>
          </q-item>

          <!-- Virtual Community Link -->
          <q-item clickable v-ripple :to="{ path: '/innovation-community', query: { tab: 'feed' } }">
            <q-item-section avatar>
              <q-icon name="groups" color="primary" />
            </q-item-section>
            <q-item-section>Innovation Community</q-item-section>
          </q-item>

          <!-- About Us Link -->
          <q-item clickable v-ripple to="/about">
            <q-item-section avatar>
              <q-icon name="info" color="primary" />
            </q-item-section>
            <q-item-section>About Us</q-item-section>
          </q-item>

          <!-- Contact Us Link -->
          <q-item clickable v-ripple to="/contact-us">
            <q-item-section avatar>
              <q-icon name="contact_support" color="primary" />
            </q-item-section>
            <q-item-section>Contact Us</q-item-section>
          </q-item>

          <q-separator spaced />

          <!-- Auth-dependent items -->
          <template v-if="isAuthenticated">
            <q-item clickable v-ripple @click="goToDashboard">
              <q-item-section avatar>
                <q-icon name="dashboard" color="primary" />
              </q-item-section>
              <q-item-section>Dashboard</q-item-section>
            </q-item>
          </template>
          <template v-else>
            <q-item clickable v-ripple @click="triggerSignIn">
              <q-item-section avatar>
                <q-icon name="login" color="primary" />
              </q-item-section>
              <q-item-section>Sign In</q-item-section>
            </q-item>
            <q-item clickable v-ripple @click="triggerSignUp">
              <q-item-section avatar>
                <q-icon name="person_add" color="primary" />
              </q-item-section>
              <q-item-section>Sign Up</q-item-section>
            </q-item>
          </template>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <!-- Glass Topbar -->
    <header class="topbar e1">
      <div class="inner">
        <div class="brand">
          <q-btn flat round dense icon="menu" aria-label="Open menu" @click="toggleLeftDrawer" />
          <q-btn flat dense class="brand-btn" @click="goHome" aria-label="Home">
            <img src="/smile-factory-logo.svg" alt="Smile Factory Logo" class="logo-image">
          </q-btn>
        </div>
        <div class="search" role="search">
          <input class="input" :placeholder="promptPlaceholder" v-model="aiPrompt" @keyup.enter="triggerAI(aiPrompt)" />
        </div>
        <div class="actions">
          <q-btn color="primary" icon="add" label="Create" @click="openCreate" />
          <q-btn flat round dense icon="notifications" aria-label="Notifications" />
          <!-- Auth-aware button group - exact copy from AppHeader -->
          <template v-if="!isAuthenticated">
            <!-- Show button group when not authenticated -->
            <q-btn-group spread rounded class="auth-btn-group">
              <q-btn
                color="primary"
                no-caps
                @click="triggerSignUp"
                style="background-color: #0D8A3E; color: white; border: none;"
                class="signup-btn"
              >
                <div class="text-caption">
                  Sign Up
                </div>
              </q-btn>
              <q-btn
                @click="triggerSignIn"
                no-caps
                style="background-color: #a4ca39; color: white; border: none;"
                class="signin-btn"
              >
                <div class="text-caption">
                  Sign In
                </div>
              </q-btn>
            </q-btn-group>
          </template>
          <template v-else>
            <!-- Show dashboard button when authenticated -->
            <q-btn
              color="green-9"
              label="Dashboard"
              @click="goToDashboard"
              class="q-px-sm dashboard-btn"
              outline
              rounded
              size="sm"
            />
          </template>
        </div>
      </div>
    </header>

    <!-- Body: three panes -->
    <div class="main">
      <!-- Left nav -->
      <aside class="left">
        <!-- Community Section -->
        <div class="panel e1 q-mb-md">
          <div class="community-title q-mb-sm">
            <q-icon name="groups" class="q-mr-sm" color="primary" size="md" />
            <span class="text-h6 text-weight-bold text-primary">Community</span>
            <span class="pulse community-pulse" aria-hidden> </span>
          </div>
          <p class="text-caption text-grey-7 q-mb-none">
            {{ getTabDescription(activeTab) }}
          </p>
        </div>

        <!-- Quick Navigation -->
        <div class="panel e1 q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Quick Navigation</div>
          <div class="quick-nav-grid">
            <RouterLink :to="{ path: '/innovation-community', query: withFlag({ tab: 'feed' }) }"
                       class="quick-nav-item"
                       :class="{ active: activeTab === 'feed' }">
              <q-icon name="home" />
              <span>Feed</span>
            </RouterLink>
            <RouterLink :to="{ path: '/innovation-community', query: withFlag({ tab: 'profiles' }) }"
                       class="quick-nav-item"
                       :class="{ active: activeTab === 'profiles' }">
              <q-icon name="people" />
              <span>Profiles</span>
            </RouterLink>
            <RouterLink :to="{ path: '/innovation-community', query: withFlag({ tab: 'events' }) }"
                       class="quick-nav-item"
                       :class="{ active: activeTab === 'events' }">
              <q-icon name="event" />
              <span>Events</span>
            </RouterLink>
            <RouterLink :to="{ path: '/innovation-community', query: withFlag({ tab: 'blog' }) }"
                       class="quick-nav-item"
                       :class="{ active: activeTab === 'blog' }">
              <q-icon name="menu_book" />
              <span>Blog</span>
            </RouterLink>
            <RouterLink :to="{ path: '/innovation-community', query: withFlag({ tab: 'marketplace' }) }"
                       class="quick-nav-item"
                       :class="{ active: activeTab === 'marketplace' }">
              <q-icon name="store" />
              <span>Market</span>
            </RouterLink>
            <div class="quick-nav-item disabled">
              <q-icon name="groups" />
              <span>Groups</span>
              <small>(Soon)</small>
            </div>
          </div>
        </div>


      </aside>

      <!-- Center content -->
      <section class="center">

        <!-- Tab content -->
        <div class="feed" v-if="activeTab==='feed'">
          <!-- Featured Section for Feed -->
          <FeaturedSection :tab="activeTab" @item-click="onFeaturedClick" class="q-mb-lg" />

          <article v-for="post in posts" :key="post.id" class="q-mb-md">
            <PostCard :post="post" @viewDetails="viewPostDetails" @comment="commentOnPost" @share="sharePost" @save="savePost" />
          </article>
          <div v-if="!loading && posts.length===0" class="empty">Nothing here yet.</div>
        </div>

        <div v-else-if="activeTab==='profiles'" class="grid">
          <!-- Featured Section for Profiles -->
          <FeaturedSection :tab="activeTab" @item-click="onFeaturedClick" class="q-mb-lg" />

          <article v-for="p in profiles" :key="p.id||p.user_id" class="q-mb-md">
            <ProfileCard :profile="p" @view="viewProfile" @message="messageProfile" @connect="connectWithProfile" />
          </article>
          <div v-if="!loading && profiles.length===0" class="empty">There are no profiles here yet.</div>
        </div>

        <div v-else-if="activeTab==='events'" class="grid">
          <!-- Featured Section for Events -->
          <FeaturedSection :tab="activeTab" @item-click="onFeaturedClick" class="q-mb-lg" />

          <article v-for="ev in events" :key="ev.id" class="q-mb-md">
            <EventCard :event="ev" @viewDetails="viewPostDetails" @register="registerForEvent" @share="shareEvent" @save="saveEvent" />
          </article>
          <div v-if="!loading && events.length===0" class="empty">There are no events yet.</div>
        </div>

        <div v-else-if="activeTab==='blog'" class="q-mt-sm">
          <BlogLayout :initial-filters="blogFilters" />
        </div>

        <div v-else-if="activeTab==='marketplace'" class="grid">
          <!-- Featured Section for Marketplace -->
          <FeaturedSection :tab="activeTab" @item-click="onFeaturedClick" class="q-mb-lg" />

          <article v-for="m in marketplace" :key="m.id" class="q-mb-md">
            <MarketplaceCard :listing="m" @view="viewMarketplaceItem" @share="shareMarketplaceItem" @contact="contactSeller" @save="saveMarketplaceItem" />
          </article>
          <div v-if="!loading && marketplace.length===0" class="empty">No products yet.</div>
        </div>
      </section>

      <!-- Right rail -->
      <aside class="right">
        <!-- Community Announcements -->
        <div class="panel e1">
          <div class="text-subtitle2 q-mb-sm">Community Announcements</div>
          <div class="text-caption text-grey-7 q-mb-sm">Latest updates and news</div>
          <div class="announcements-list">
            <div class="announcement-item q-mb-sm">
              <q-icon name="campaign" color="primary" class="q-mr-sm" />
              <span class="text-body2">Welcome to the new community experience!</span>
            </div>
            <div class="announcement-item q-mb-sm">
              <q-icon name="event" color="secondary" class="q-mr-sm" />
              <span class="text-body2">Upcoming mentorship events this week</span>
            </div>
            <div class="announcement-item">
              <q-icon name="info" color="info" class="q-mr-sm" />
              <span class="text-body2">New features coming soon</span>
            </div>
          </div>
        </div>

        <!-- Latest Featured -->
        <div class="panel e1">
          <div class="text-subtitle2 q-mb-sm">Latest Featured</div>
          <div class="text-caption text-grey-7 q-mb-sm">Trending content across the platform</div>
          <q-btn flat dense icon="refresh" label="Refresh" @click="reloadCurrentTab" size="sm" />
        </div>

        <!-- Mentorship Hub (if applicable) -->
        <div v-if="showMentorship" class="panel e1">
          <div class="text-subtitle2 q-mb-sm">Mentorship Hub</div>
          <div class="text-caption text-grey-7 q-mb-sm">Manage your sessions and requests</div>
          <RouterLink :to="'/dashboard/mentorship'" class="q-btn q-btn--flat q-btn--dense">
            <q-icon name="hub" class="q-mr-xs" />
            Open Hub
          </RouterLink>
        </div>

        <!-- Entrepreneurs -->
        <div class="panel e1">
          <div class="text-subtitle2 q-mb-sm">Entrepreneurs</div>
          <div class="text-caption text-grey-7 q-mb-sm">Connect with business innovators</div>
          <q-btn flat dense @click="triggerAI('find entrepreneurs')" size="sm">
            <q-icon name="business" class="q-mr-xs" />
            Discover
          </q-btn>
        </div>

        <!-- Mentors -->
        <div class="panel e1">
          <div class="text-subtitle2 q-mb-sm">Mentors</div>
          <div class="text-caption text-grey-7 q-mb-sm">Find guidance and expertise</div>
          <q-btn flat dense @click="triggerAI('find mentors')" size="sm">
            <q-icon name="school" class="q-mr-xs" />
            Find Mentors
          </q-btn>
        </div>
      </aside>
    </div>

    <!-- Post Creation Dialog -->
    <PostCreationDialog
      v-model="showCreateDialog"
      :active-tab="activeTab"
      @post-created="onPostCreated"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter, RouterLink } from 'vue-router'
import { useFilterStore } from '../../stores/filterStore'
import { useNotificationStore } from '../../stores/notifications'
import { useContentInteractions } from '../../composables/useContentInteractions'
import { useGlobalServicesStore } from '../../stores/globalServices'
import { useProfileStore } from '../../stores/profile'
import { useAuthStore } from '../../stores/auth'
import { feedDataService } from '../../services/feedDataService'
import { triggerSignIn, triggerSignUp } from '../../services/unifiedAuthService'
import FeaturedSection from '../../components/feed/FeaturedSection.vue'
import PostCard from '../../components/feed/cards/PostCard.vue'
import ProfileCard from '../../components/feed/cards/ProfileCard.vue'
import EventCard from '../../components/feed/cards/EventCard.vue'
import MarketplaceCard from '../../components/feed/cards/MarketplaceCard.vue'
import BlogLayout from '../../components/blog/BlogLayout.vue'
import PostCreationDialog from '../../components/feed/PostCreationDialog.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const activeTab = computed(() => {
  const t = (route.query.tab as string) || 'feed'
  return ['feed','profiles','events','blog','marketplace'].includes(t) ? t : 'feed'
})

// Authentication state
const isAuthenticated = computed(() => authStore.isAuthenticated)

// Drawer state
const leftDrawerOpen = ref(false)

// Post creation state
const showCreateDialog = ref(false)

const filterStore = useFilterStore()
const notifications = useNotificationStore()
const contentInteractions = useContentInteractions()
const globalServices = useGlobalServicesStore()
const cache = globalServices.cacheService
const profileStore = useProfileStore()

const showMentorship = computed(() => {
  const type = profileStore.currentProfile?.profile_type
  return type === 'innovator' || type === 'academic_student' || type === 'mentor'
})

const posts = ref<any[]>([])
const profiles = ref<any[]>([])
const events = ref<any[]>([])
const marketplace = ref<any[]>([])
const loading = ref(false)

const blogFilters = computed(() => ({
  searchQuery: filterStore.currentFilters?.searchQuery || '',
  category: filterStore.currentFilters?.category || '',
  tags: filterStore.currentFilters?.blogCategories || []
}))

const aiPrompt = ref('')
const composerText = ref('')
const promptPlaceholder = computed(() => `Ask anything… (AI search)`) 

function withFlag(q: Record<string, any>) {
  return { newUI: '1', ...(route.query || {}), ...q }
}

// Navigation functions
function toggleLeftDrawer() {
  leftDrawerOpen.value = !leftDrawerOpen.value
}

function goHome() {
  router.push('/')
  leftDrawerOpen.value = false
}

function goToDashboard() {
  router.push('/dashboard')
  leftDrawerOpen.value = false
}

// Post creation functions
function openCreate() {
  if (!isAuthenticated.value) {
    triggerSignIn()
    return
  }
  showCreateDialog.value = true
}

function onPostCreated(post: any) {
  // Refresh the current tab data
  reloadCurrentTab()
}

function noop() {}
function onFeaturedClick() {}

// Get tab description
function getTabDescription(tab: string) {
  const descriptions = {
    feed: 'Discover the latest posts, updates, and conversations from our community',
    profiles: 'Connect with innovators, students, mentors, and entrepreneurs',
    events: 'Find upcoming events, workshops, and networking opportunities',
    blog: 'Read inspiring stories, insights, and knowledge from our community',
    marketplace: 'Explore products, services, and opportunities from our members'
  }
  return descriptions[tab as keyof typeof descriptions] || 'Explore our vibrant community'
}

async function reloadCurrentTab(){ await loadTabData(activeTab.value,true) }

async function loadTabData(tab: string, force=false){
  const cacheKey = `newui:tab:${tab}`
  if (!force && cache.get<boolean>(cacheKey)) return
  loading.value = true
  try{
    switch(tab){
      case 'feed': {
        const result = await feedDataService.fetchFeedPosts({ page:1, limit:10, cursor:null, scrollPosition:0 }, filterStore.currentFilters)
        posts.value = result.posts
        break
      }
      case 'profiles': {
        const result = await feedDataService.fetchProfiles({ page:1, limit:12, cursor:null, scrollPosition:0 }, filterStore.currentFilters)
        profiles.value = result.profiles || []
        break
      }
      case 'events': {
        const result = await feedDataService.fetchEvents({ page:1, limit:12, cursor:null, scrollPosition:0 }, filterStore.currentFilters)
        events.value = result.events || []
        break
      }
      case 'marketplace': {
        const result = await feedDataService.fetchMarketplace({ page:1, limit:12, cursor:null, scrollPosition:0 }, filterStore.currentFilters)
        marketplace.value = result.marketplace || []
        break
      }
    }
  } finally {
    loading.value = false
    cache.set(cacheKey,true,{ ttl: 30*1000, storage:'memory' })
  }
}

onMounted(async ()=>{
  filterStore.setActiveTab(activeTab.value,false)
  await loadTabData(activeTab.value)
})

watch(() => route.query.tab, async (t)=>{
  filterStore.setActiveTab(activeTab.value,false)
  await loadTabData(activeTab.value,true)
})

// Interaction pass-throughs
function viewPostDetails(id:number){ const post = posts.value.find(p=>p.id===id); contentInteractions.viewContent(id,'post',post) }
function commentOnPost({ postId, comment }:{postId:number, comment:string}){ notifications.info('Comment (new UI)') }
function sharePost(postId:number){ const post = posts.value.find(p=>p.id===postId); contentInteractions.shareContent(postId,'post',post) }
function savePost(postId:number){ const post = posts.value.find(p=>p.id===postId); contentInteractions.toggleSaveContent(postId,'post',post) }

function viewProfile(id:string){ /* route handled in card */ }
async function messageProfile(id:string){ notifications.info('Message via new UI'); }
async function connectWithProfile(id:string){ notifications.info('Connect via new UI'); }

function registerForEvent(){ notifications.info('Register via new UI') }
function shareEvent(eventId:number){ const ev = events.value.find(e=>e.id===eventId); contentInteractions.shareContent(eventId,'event',ev) }
function saveEvent(eventId:number){ const ev = events.value.find(e=>e.id===eventId); contentInteractions.toggleSaveContent(eventId,'event',ev) }

function viewMarketplaceItem(){ }
function shareMarketplaceItem(id:number){ const m = marketplace.value.find(x=>x.id===id); contentInteractions.shareContent(id,'post',m) }
function contactSeller(){ }
function saveMarketplaceItem(id:number){ const m = marketplace.value.find(x=>x.id===id); contentInteractions.toggleSaveContent(id,'post',m) }

async function triggerAI(query:string){
  try{
    const { useAiChatTriggerService } = await import('../../services/aiChatTriggerService')
    const ai = useAiChatTriggerService()
    // Use general ai_search trigger when free text is provided
    if(query && query.trim().length>0){
      await ai.sendMessage(query.trim())
    }else{
      await ai.triggerChat('ai_search', `community-${activeTab.value}`)
    }
  }catch(err){
    console.error('AI trigger error (new UI):', err)
    notifications.error('Failed to trigger AI search')
  }
}
</script>

<style scoped>
/* Glass shell styles inspired by docs/templates/shared.css */
.e1{box-shadow:0 1px 2px rgba(0,0,0,.06),0 1px 8px rgba(0,0,0,.04)}
.e2{box-shadow:0 8px 24px rgba(0,0,0,.14)}
/* Light background matching legacy UI */
.glass-community-page{
  background: #f8f9fa;
  min-height:100vh;
}
/* Clean topbar with ZB Innovation colors */
.topbar{
  position:sticky;
  top:0;
  z-index:50;
  background: rgba(255,255,255,0.95);
  backdrop-filter: blur(8px);
  border-bottom:1px solid rgba(0,0,0,.08);
}
.topbar .inner{display:flex;align-items:center;gap:12px;padding:10px 16px}
.brand{display:flex;align-items:center;gap:8px}
.brand-btn{
  background:transparent;
  color: #245926;
  font-weight: 600;
}
.community-btn{
  position:relative;
  background: linear-gradient(135deg, #245926, #74b524);
  color: white;
  border-radius: 8px;
  padding: 8px 16px;
}
.community-btn .pulse{
  width:8px;
  height:8px;
  background:#74b524;
  border-radius:999px;
  margin-left:6px;
  display:inline-block;
  animation:pulse 1.8s infinite;
}
@keyframes pulse{
  0%{box-shadow:0 0 0 0 rgba(116,181,36,.7)}
  70%{box-shadow:0 0 0 10px rgba(116,181,36,0)}
  100%{box-shadow:0 0 0 0 rgba(116,181,36,0)}
}
.search{flex:1;display:flex;gap:8px;align-items:center;justify-content:center}
.input{
  max-width:500px;
  width:100%;
  background:white;
  border:1px solid #e5e7eb;
  border-radius:8px;
  padding:12px 16px;
  outline:0;
  color:#374151;
  font-size: 14px;
}
.input::placeholder{color:#9ca3af}
.input:focus{
  border-color:#74b524;
  box-shadow:0 0 0 3px rgba(116,181,36,.1);
}
.chips{display:flex;gap:8px;flex-wrap:wrap}
.chip{
  border:1px solid #e5e7eb;
  background:#fff;
  border-radius:16px;
  padding:6px 12px;
  font-size:12px;
  cursor:pointer;
  transition: all 0.2s ease;
}
.chip:hover{
  background:#f3f4f6;
  border-color:#74b524;
}

/* Auth button group styles - copied from MainLayout.vue */
.auth-btn-group {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin: 0 4px;
  border-radius: 20px !important;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.auth-btn-group .q-btn {
  flex: 1 1 50% !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 50% !important;
  box-shadow: none;
}

/* First button in group (Sign Up) */
.auth-btn-group .q-btn:first-child {
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 20px !important;
}

/* Last button in group (Sign In) */
.auth-btn-group .q-btn:last-child {
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
}

.dashboard-btn {
  font-size: 0.8rem;
  padding: 4px 12px;
}

/* Quick navigation grid styles */
.quick-nav-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.quick-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border-radius: 12px;
  text-decoration: none;
  color: #6b7280;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  min-height: 70px;
  justify-content: center;
}

.quick-nav-item:hover {
  background: #f3f4f6;
  border-color: #74b524;
  color: #245926;
  transform: translateY(-1px);
}

.quick-nav-item.active {
  background: linear-gradient(90deg, rgba(36,89,38,.1), rgba(116,181,36,.05));
  border-color: #74b524;
  color: #245926;
  font-weight: 500;
}

.quick-nav-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quick-nav-item .q-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.quick-nav-item span {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.quick-nav-item small {
  font-size: 10px;
  color: #9ca3af;
  margin-top: 2px;
}

/* Community announcements styles */
.announcements-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.announcement-item {
  display: flex;
  align-items: flex-start;
  padding: 8px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.announcement-item .q-icon {
  margin-top: 2px;
  font-size: 16px;
}

.announcement-item .text-body2 {
  font-size: 13px;
  line-height: 1.4;
}

/* Community pulse animation for left sidebar */
.community-pulse {
  width: 8px;
  height: 8px;
  background: #74b524;
  border-radius: 999px;
  margin-left: 6px;
  display: inline-block;
  animation: pulse 1.8s infinite;
}
.main{display:grid;grid-template-columns:260px minmax(0,1fr) 320px;gap:16px;padding:8px 16px}
.nav{background:#fff;border-radius:18px;padding:10px;border:1px solid #e5e7eb}
.nav a{display:flex;align-items:center;gap:10px;padding:10px;border-radius:10px;color:#374151;text-decoration:none}
.nav a[aria-current="page"]{
  background:linear-gradient(90deg,rgba(36,89,38,.1),rgba(116,181,36,.05));
  color:#245926;
  font-weight: 500;
}
.nav a:hover{
  background:#f3f4f6;
}
.nav .soon{opacity:.5;cursor:not-allowed;padding:10px}
.left{position:sticky;top:72px;height:calc(100vh - 80px)}
.right{position:sticky;top:72px;height:calc(100vh - 80px);display:flex;flex-direction:column;gap:16px}
.panel{background:#fff;border-radius:18px;border:1px solid #e5e7eb;padding:12px}
.shelf{background:rgba(255,255,255,.7);border:1px solid #e5e7eb;border-radius:24px;padding:10px 8px;backdrop-filter:blur(10px)}
/* Content areas */
.center {
  margin-top: 0;
  padding-top: 0;
}

.feed{
  display:flex;
  flex-direction:column;
  gap:16px;
}
.grid{
  display:grid;
  grid-template-columns:repeat(auto-fill,minmax(300px,1fr));
  gap:16px;
}

/* Profile card styling */
.grid article {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
}

.grid article:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,.08);
  transform: translateY(-1px);
}

/* Profile type indicators */
.grid article[data-profile-type="innovator"] {
  border-top: 4px solid #5b8def;
}

.grid article[data-profile-type="academic_student"] {
  border-top: 4px solid #6ee7b7;
}

.grid article[data-profile-type="mentor"] {
  border-top: 4px solid #f472b6;
}

.grid article[data-profile-type="entrepreneur"] {
  border-top: 4px solid #fbbf24;
}

.profile-type-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 2;
}

.profile-type-badge.innovator {
  background: #5b8def;
  color: white;
}

.profile-type-badge.academic_student {
  background: #6ee7b7;
  color: #065f46;
}

.profile-type-badge.mentor {
  background: #f472b6;
  color: white;
}

.profile-type-badge.entrepreneur {
  background: #fbbf24;
  color: #92400e;
}
.empty{
  border:2px dashed #d1d5db;
  border-radius:12px;
  padding:40px;
  color:#6b7280;
  text-align:center;
  background:white;
}

/* Enhanced post card styling for different content types */
.feed article {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
}

.feed article:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,.08);
  transform: translateY(-1px);
}

/* Blog post cards - emphasis on content */
.feed article[data-post-type="blog"] {
  border-left: 4px solid #8b5cf6;
}

.feed article[data-post-type="blog"] .post-type-indicator {
  background: linear-gradient(135deg, #8b5cf6, #a78bfa);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 2;
}

/* Event cards - emphasis on date and time */
.feed article[data-post-type="event"] {
  border-left: 4px solid #10b981;
}

.feed article[data-post-type="event"] .post-type-indicator {
  background: linear-gradient(135deg, #10b981, #34d399);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 2;
}

.feed article[data-post-type="event"] .event-date-badge {
  background: #10b981;
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(16,185,129,.3);
}

/* Marketplace cards - emphasis on product/service */
.feed article[data-post-type="marketplace"] {
  border-left: 4px solid #f59e0b;
}

.feed article[data-post-type="marketplace"] .post-type-indicator {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 2;
}

.feed article[data-post-type="marketplace"] .price-badge {
  background: #f59e0b;
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 14px;
  position: absolute;
  bottom: 16px;
  right: 16px;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(245,158,11,.3);
}

/* Opportunity cards - emphasis on urgency */
.feed article[data-post-type="opportunity"] {
  border-left: 4px solid #ef4444;
}

.feed article[data-post-type="opportunity"] .post-type-indicator {
  background: linear-gradient(135deg, #ef4444, #f87171);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 2;
}

.feed article[data-post-type="opportunity"] .deadline-badge {
  background: #ef4444;
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 12px;
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(239,68,68,.3);
}

/* General post cards - clean and simple */
.feed article[data-post-type="general"] {
  border-left: 4px solid #245926;
}

.feed article[data-post-type="general"] .post-type-indicator {
  background: linear-gradient(135deg, #245926, #74b524);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 2;
}
/* Logo styling */
.logo-image {
  height: 32px;
  width: auto;
}

/* Mobile drawer styling */
.mobile-nav-drawer {
  background-color: white;
}

.mobile-nav-drawer .q-item {
  border-radius: 8px;
  margin: 4px 8px;
}

.mobile-nav-drawer .q-item:hover {
  background-color: rgba(36,89,38,.05);
}

/* Auth button group styling */
.auth-button-group {
  display: flex;
  align-items: center;
  margin-left: 8px;
}

.auth-btn {
  padding: 6px 16px;
  font-weight: 500;
  text-transform: none;
  min-height: 32px;
}

.auth-btn-left {
  border-radius: 6px 0 0 6px;
  border-right: 1px solid rgba(255,255,255,0.2);
}

.auth-btn-right {
  border-radius: 0 6px 6px 0;
}

.auth-btn:not(.auth-btn-left):not(.auth-btn-right) {
  border-radius: 6px;
}

/* Community header styling */
.community-header {
  text-align: center;
  padding: 16px 0;
}

.community-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
}

.community-pulse {
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
}

/* Quick navigation grid */
.quick-nav-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.quick-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border-radius: 8px;
  text-decoration: none;
  color: #6b7280;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
  background: #fafafa;
}

.quick-nav-item:hover {
  background: #f3f4f6;
  color: #245926;
  border-color: #245926;
  text-decoration: none;
}

.quick-nav-item.active {
  background: #245926;
  color: white;
  border-color: #245926;
}

.quick-nav-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quick-nav-item .q-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.quick-nav-item span {
  font-size: 12px;
  font-weight: 500;
}

.quick-nav-item small {
  font-size: 10px;
  opacity: 0.7;
}

/* Button styling improvements */
.q-btn {
  text-transform: none;
  font-weight: 500;
}

.q-btn.q-btn--flat {
  transition: all 0.2s ease;
}

.q-btn.q-btn--flat:hover {
  background-color: rgba(0,0,0,.04);
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .topbar .brand {
    gap: 8px;
  }

  .topbar .search {
    margin: 0 8px;
  }

  .topbar .search .input {
    font-size: 14px;
    padding: 8px 12px;
  }

  .topbar .chips {
    gap: 4px;
  }

  .topbar .chip {
    padding: 4px 8px;
    font-size: 12px;
  }

  .topbar .actions {
    gap: 4px;
  }

  .auth-btn {
    padding: 4px 12px;
    font-size: 13px;
  }

  .main-content {
    padding: 4px;
  }

  .main {
    padding: 4px 8px;
  }

  .sidebar {
    display: none;
  }

  .center {
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
    padding-top: 0;
  }

  .featured-section {
    padding: 12px;
  }

  .featured-cards {
    gap: 12px;
  }

  .featured-card {
    min-width: 280px;
  }

  .feed article {
    margin-bottom: 12px;
  }

  .grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .topbar {
    padding: 8px 12px;
  }

  .topbar .search .input {
    font-size: 13px;
    padding: 6px 10px;
  }

  .topbar .chip {
    padding: 3px 6px;
    font-size: 11px;
  }

  .featured-card {
    min-width: 260px;
  }

  .auth-btn {
    padding: 3px 8px;
    font-size: 12px;
  }
}

/* Responsive design */
@media (max-width: 1024px){
  .main{
    grid-template-columns:1fr;
    gap:16px;
    padding:16px 8px;
  }
  .left,.right{
    display:none;
  }
  .topbar .inner{
    padding:12px 8px;
  }
  .search{
    max-width:none;
  }
}
</style>

