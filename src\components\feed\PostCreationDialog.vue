<template>
  <q-dialog
    v-model="dialogOpen"
    persistent
    maximized
    :transition-show="'slide-up'"
    :transition-hide="'slide-down'"
  >
    <q-card class="post-creation-dialog">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">{{ dialogTitle }}</div>
        <q-space />
        <div class="row q-gutter-sm q-mr-md">
          <q-btn
            color="primary"
            icon="psychology"
            label="Get Ideas"
            size="sm"
            outline
            @click="handleAITrigger('content_ideas')"
            v-if="isAuthenticated"
          >
            <q-tooltip>Get AI help with content ideas</q-tooltip>
          </q-btn>
          <q-btn
            color="secondary"
            icon="psychology"
            label="Writing Help"
            size="sm"
            outline
            @click="handleAITrigger('writing_assistance')"
            v-if="isAuthenticated"
          >
            <q-tooltip>Get AI writing assistance</q-tooltip>
          </q-btn>
        </div>
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-separator class="q-my-md" />

      <q-card-section class="q-pa-md scroll" style="max-height: 80vh">
        <!-- Authentication Check -->
        <div v-if="!isAuthenticated" class="auth-required">
          <div class="text-center q-pa-lg">
            <q-icon name="lock" size="4rem" color="primary" />
            <h5 class="q-mt-md q-mb-sm">Authentication Required</h5>
            <p class="q-mb-lg">You need to sign in to create a post.</p>

            <!-- Auth Options Component -->
            <auth-options
              mode="login"
              @email-password="showEmailPasswordForm = true"
              @email-password-signup="showEmailPasswordSignupForm = true"
            />
          </div>

          <!-- Sign In Form Dialog -->
          <q-dialog v-model="showEmailPasswordForm">
            <q-card style="min-width: 350px">
              <q-card-section>
                <div class="text-h6">Sign In</div>
              </q-card-section>

              <q-card-section>
                <q-input
                  v-model="signInForm.email"
                  label="Email"
                  type="email"
                  outlined
                  :rules="emailRules"
                >
                  <template v-slot:prepend>
                    <unified-icon name="mail" />
                  </template>
                </q-input>

                <q-input
                  v-model="signInForm.password"
                  :type="isPwd ? 'password' : 'text'"
                  label="Password"
                  outlined
                  class="q-mt-md"
                  :rules="passwordRules"
                >
                  <template v-slot:prepend>
                    <unified-icon name="lock" />
                  </template>
                  <template v-slot:append>
                    <div class="cursor-pointer" @click="isPwd = !isPwd">
                      <unified-icon :name="isPwd ? 'eye-off' : 'eye'" />
                    </div>
                  </template>
                </q-input>
              </q-card-section>

              <q-card-section class="text-center q-pt-none">
                <q-btn
                  flat
                  dense
                  color="primary"
                  label="Forgot Password?"
                  to="/password-reset"
                  :disable="loading"
                  v-close-popup
                />
              </q-card-section>

              <q-card-actions align="right">
                <q-btn flat label="Cancel" color="grey" v-close-popup />
                <q-btn
                  label="Sign In"
                  color="primary"
                  @click="handleEmailPasswordSignIn"
                  :loading="loading"
                />
              </q-card-actions>
            </q-card>
          </q-dialog>

          <!-- Sign Up Form Dialog -->
          <q-dialog v-model="showEmailPasswordSignupForm">
            <q-card style="min-width: 350px">
              <q-card-section>
                <div class="text-h6">Sign Up</div>
              </q-card-section>

              <q-card-section>
                <q-input
                  v-model="signUpForm.email"
                  label="Email"
                  type="email"
                  outlined
                  :rules="emailRules"
                >
                  <template v-slot:prepend>
                    <unified-icon name="mail" />
                  </template>
                </q-input>

                <q-input
                  v-model="signUpForm.password"
                  :type="isSignupPwd ? 'password' : 'text'"
                  label="Password"
                  outlined
                  class="q-mt-md"
                  :rules="passwordRules"
                >
                  <template v-slot:prepend>
                    <unified-icon name="lock" />
                  </template>
                  <template v-slot:append>
                    <div class="cursor-pointer" @click="isSignupPwd = !isSignupPwd">
                      <unified-icon :name="isSignupPwd ? 'eye-off' : 'eye'" />
                    </div>
                  </template>
                </q-input>

                <q-input
                  v-model="signUpForm.confirmPassword"
                  :type="isSignupConfirmPwd ? 'password' : 'text'"
                  label="Confirm Password"
                  outlined
                  class="q-mt-md"
                  :rules="[
                    val => !!val || 'Please confirm your password',
                    val => val === signUpForm.password || 'Passwords do not match'
                  ]"
                >
                  <template v-slot:prepend>
                    <unified-icon name="lock" />
                  </template>
                  <template v-slot:append>
                    <div class="cursor-pointer" @click="isSignupConfirmPwd = !isSignupConfirmPwd">
                      <unified-icon :name="isSignupConfirmPwd ? 'eye-off' : 'eye'" />
                    </div>
                  </template>
                </q-input>
              </q-card-section>

              <q-card-actions align="right">
                <q-btn flat label="Cancel" color="grey" v-close-popup />
                <q-btn
                  label="Sign Up"
                  color="primary"
                  @click="handleEmailPasswordSignUp"
                  :loading="loading"
                />
              </q-card-actions>
            </q-card>
          </q-dialog>
        </div>

        <!-- Post Creation Forms -->
        <div v-else>
          <!-- Post Type Selector -->
          <div class="post-type-selector q-mb-md">
            <q-select
              v-model="currentPostType"
              :options="postTypeOptions"
              label="Post Type"
              outlined
              emit-value
              map-options
              @update:model-value="handlePostTypeChange"
            >
              <template v-slot:prepend>
                <q-icon name="post_add" />
              </template>
              <template v-slot:option="scope">
                <q-item v-bind="scope.itemProps" :disable="scope.opt.disabled">
                  <q-item-section avatar>
                    <q-icon :name="scope.opt.icon" :color="scope.opt.disabled ? 'grey' : scope.opt.color" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label :class="scope.opt.disabled ? 'text-grey' : ''">{{ scope.opt.label }}</q-item-label>
                    <q-item-label caption :class="scope.opt.disabled ? 'text-grey' : ''">{{ scope.opt.description }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </q-select>
          </div>

          <!-- Dynamic form based on post type and active tab -->
          <component
            :is="currentFormComponent"
            v-if="currentFormComponent"
            @submit="handleFormSubmit"
            @cancel="dialogOpen = false"
            :loading="loading"
          />
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits } from 'vue';
import { useAuthStore } from '../../stores/auth';
import { useNotificationStore } from '../../stores/notifications';
import { usePostsStore } from '../../stores/posts';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import AIChatTriggerService from '../../services/aiChatTriggerService';
import AuthOptions from '../../components/auth/AuthOptions.vue';
import UnifiedIcon from '../../components/ui/UnifiedIcon.vue';

// Import form components
import GeneralPostForm from './forms/GeneralPostForm.vue';
import OpportunityPostForm from './forms/OpportunityPostForm.vue';
import BlogPostForm from './forms/BlogPostForm.vue';
import EventPostForm from './forms/EventPostForm.vue';
import GroupPostForm from './forms/GroupPostForm.vue';
import MarketplacePostForm from './forms/MarketplacePostForm.vue';

const props = withDefaults(defineProps<{
  modelValue?: boolean;
  postType?: string;
  activeTab: string;
}>(), {
  modelValue: false,
  postType: 'opportunity'
});

// Create a local ref for postType that can be modified
const currentPostType = ref(props.postType || 'opportunity');

const emit = defineEmits(['update:modelValue', 'post-created']);

// State
const dialogOpen = ref(props.modelValue);
const showEmailPasswordForm = ref(false);
const showEmailPasswordSignupForm = ref(false);
const loading = ref(false);
const isPwd = ref(true);
const isSignupPwd = ref(true);
const isSignupConfirmPwd = ref(true);

// Post type options for the dropdown
const postTypeOptions = [
  {
    label: 'Opportunity',
    value: 'opportunity',
    icon: 'emoji_objects',
    color: 'amber',
    description: 'Share funding, collaboration, or job opportunities'
  },
  {
    label: 'Blog Article',
    value: 'blog',
    icon: 'article',
    color: 'deep-orange',
    description: 'Write a detailed article or story'
  },
  {
    label: 'Event',
    value: 'event',
    icon: 'event',
    color: 'green',
    description: 'Announce an upcoming event or program'
  },
  {
    label: 'Marketplace Listing',
    value: 'marketplace',
    icon: 'storefront',
    color: 'purple',
    description: 'List a product, service, or resource'
  },
  // Group creation is temporarily disabled
  // {
  //   label: 'Group',
  //   value: 'group',
  //   icon: 'groups',
  //   color: 'blue',
  //   description: 'Create a new community group'
  // },
  {
    label: 'General Post (Coming Soon)',
    value: 'general',
    icon: 'post_add',
    color: 'grey',
    description: 'Share updates, questions, or discussions',
    disabled: true
  }
];

// Form data
const signInForm = ref({ email: '', password: '' });
const signUpForm = ref({
  email: '',
  password: '',
  confirmPassword: ''
});

// Get stores
const authStore = useAuthStore();
const notificationStore = useNotificationStore();
const $q = useQuasar();
const router = useRouter();
const isAuthenticated = computed(() => authStore.isAuthenticated);

// Form validation rules
const emailRules = [
  (val: string) => !!val || 'Email is required',
  (val: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || 'Please enter a valid email'
];

const passwordRules = [
  (val: string) => !!val || 'Password is required',
  (val: string) => val.length >= 8 || 'Password must be at least 8 characters'
];

// Watch for changes in modelValue prop
watch(() => props.modelValue, (newVal) => {
  dialogOpen.value = newVal;
});

// Watch for changes in dialog state
watch(dialogOpen, (newVal) => {
  emit('update:modelValue', newVal);
});

// Watch for changes in props.postType
watch(() => props.postType, (newVal) => {
  currentPostType.value = newVal;
});

// Watch for changes in activeTab to update the default post type
watch(() => props.activeTab, (newTab) => {
  // Only update if the dialog is about to open (not already open)
  if (!dialogOpen.value) {
    // Map the active tab to the corresponding post type
    const tabToPostTypeMap = {
      feed: 'opportunity',
      blog: 'blog',
      events: 'event',
      groups: 'opportunity', // Changed from 'group' to 'opportunity' since group creation is disabled
      marketplace: 'marketplace',
      profiles: 'opportunity'
    };

    // Set the current post type based on the active tab
    currentPostType.value = tabToPostTypeMap[newTab] || 'opportunity';

    // We've removed the notification here to prevent duplicate notifications
    // The notification will be shown only once when the dialog is opened
  }
});

// Watch for changes in authentication state
watch(() => authStore.isAuthenticated, (newVal) => {
  // If user becomes authenticated while dialog is open, close the auth forms
  if (newVal) {
    showEmailPasswordForm.value = false;
    showEmailPasswordSignupForm.value = false;
  }
});

// Handle post type change from dropdown
function handlePostTypeChange(newPostType) {
  console.log('Post type changed to:', newPostType);

  // Prevent selection of disabled options
  if (newPostType === 'general') {
    // Show notification and revert to opportunity
    notificationStore.info('General posts are coming soon! Please use other post types for now.');
    currentPostType.value = 'opportunity';
    return;
  }

  currentPostType.value = newPostType;
}

// Computed properties
const dialogTitle = computed(() => {
  if (!isAuthenticated.value) return 'Authentication Required';

  switch (currentPostType.value) {
    case 'general':
      return 'Create Post';
    case 'opportunity':
      return 'Create Opportunity';
    case 'blog':
      return 'Create Blog Article';
    case 'event':
      return 'Create Event';
    case 'group':
      return 'Create Group';
    case 'marketplace':
      return 'Create Marketplace Listing';
    default:
      return 'Create Post';
  }
});

const currentFormComponent = computed(() => {
  switch (currentPostType.value) {
    case 'general':
      return GeneralPostForm;
    case 'opportunity':
      return OpportunityPostForm;
    case 'blog':
      return BlogPostForm;
    case 'event':
      return EventPostForm;
    case 'group':
      // Only show the notification once when the post type is explicitly set to 'group'
      // This prevents the infinite notification issue
      if (dialogOpen.value) {
        // Use setTimeout to ensure this only runs once after the component is mounted
        setTimeout(() => {
          notificationStore.info('Group creation is temporarily disabled. Please check back later.');
        }, 0);
      }
      // Return null to prevent showing the form
      return null;
    case 'marketplace':
      return MarketplacePostForm;
    default:
      return null;
  }
});

// Methods
async function handleEmailPasswordSignIn() {
  try {
    loading.value = true;

    // Basic validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!signInForm.value.email || !emailRegex.test(signInForm.value.email)) {
      throw new Error('Please enter a valid email address');
    }

    if (!signInForm.value.password) {
      throw new Error('Password is required');
    }

    // Create a custom sign-in function that doesn't redirect
    const originalPush = router.push;

    // Temporarily override router.push to prevent redirection
    router.push = (location) => {
      console.log('Preventing redirection to:', location);
      // Return a resolved promise to satisfy the router
      return Promise.resolve(false);
    };

    try {
      // Use auth store for sign in
      await authStore.signIn(signInForm.value.email, signInForm.value.password);

      // Close the dialog
      showEmailPasswordForm.value = false;

      // Show success notification
      notificationStore.success('Successfully signed in! You can now create your post.');
    } finally {
      // Restore original router.push
      router.push = originalPush;
    }
  } catch (error: any) {
    console.error('Sign in error:', error);
    notificationStore.error(error.message || 'Failed to sign in. Please check your credentials.');
  } finally {
    loading.value = false;
  }
}

async function handleEmailPasswordSignUp() {
  try {
    loading.value = true;

    // Basic validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!signUpForm.value.email || !emailRegex.test(signUpForm.value.email)) {
      throw new Error('Please enter a valid email address');
    }

    if (!signUpForm.value.password) {
      throw new Error('Password is required');
    }

    if (signUpForm.value.password !== signUpForm.value.confirmPassword) {
      throw new Error('Passwords do not match');
    }

    // Create a custom sign-up function that doesn't redirect
    const originalPush = router.push;

    // Temporarily override router.push to prevent redirection
    router.push = (location) => {
      console.log('Preventing redirection to:', location);
      // Return a resolved promise to satisfy the router
      return Promise.resolve(false);
    };

    try {
      // Use auth store for sign up
      await authStore.signUp({
        email: signUpForm.value.email,
        password: signUpForm.value.password
      });

      // Close the dialog
      showEmailPasswordSignupForm.value = false;

      // Show success notification
      notificationStore.success('Account created successfully! You can now create your post.');
    } finally {
      // Restore original router.push
      router.push = originalPush;
    }
  } catch (error: any) {
    console.error('Sign up error:', error);
    notificationStore.error(error.message || 'Failed to sign up. Please try again.');
  } finally {
    loading.value = false;
  }
}

// Method to open the dialog with a specific post type
function openDialog(postType: string) {
  // Set the post type
  currentPostType.value = postType;

  // Open the dialog
  dialogOpen.value = true;

  console.log('Dialog opened with post type:', postType, 'based on active tab:', props.activeTab);
}

async function handleFormSubmit(formData: any) {
  console.log('Form submitted to parent component, setting loading state to true');
  loading.value = true;
  try {
    // Get the posts store
    const postsStore = usePostsStore();

    // Make sure postType is set - always use 'platform' for user-created posts
    // This is required by the database constraint
    formData.post_type = 'platform';

    // Set the frontend postType based on the current form type
    if (!formData.postType) {
      formData.postType = currentPostType.value;
    }

    // Make sure subType is set - this should match the current form type
    if (!formData.subType) {
      formData.subType = currentPostType.value;
    }

    // Set the database sub_type field to match the frontend subType
    formData.sub_type = formData.subType;

    // Process tags to ensure they're all strings (not objects)
    if (formData.tags && Array.isArray(formData.tags)) {
      formData.tags = formData.tags.map(tag => {
        if (typeof tag === 'object' && tag !== null) {
          return tag.value || tag.label || String(tag);
        }
        return String(tag);
      });
    }

    // Special handling for marketplace listings
    if (formData.postType === 'marketplace') {
      // Ensure the post type is set to 'platform' to match database constraints
      formData.postType = 'platform';

      // Make sure we have the right image format
      if (formData.images && formData.images.length > 0 && !formData.featuredImage) {
        formData.featuredImage = formData.images[0];
      }

      // If mediaUrls is provided, ensure it's properly formatted for the database
      if (formData.mediaUrls && Array.isArray(formData.mediaUrls)) {
        formData.media_urls = formData.mediaUrls;
      }

      // Ensure content is properly formatted as JSON if it's not already
      if (formData.content && typeof formData.content === 'string') {
        try {
          // Check if it's already JSON
          JSON.parse(formData.content);
        } catch (e) {
          // If not JSON, convert it to JSON with the description
          formData.content = JSON.stringify({
            description: formData.content,
            marketplaceDetails: {}
          });
        }
      }
    }

    // Ensure all required fields are present
    const postData = {
      ...formData,
      // Convert image to featuredImage if needed
      featuredImage: formData.featuredImage || formData.image || null,
      // Ensure status is set (must be lowercase to match database constraint)
      status: formData.status || 'published'
    };

    // Special handling for blog posts
    if (formData.postType === 'blog' || formData.postType === 'BLOG') {
      // Ensure blog_category is set for database compatibility
      if (formData.category && !(formData as any).blog_category) {
        (postData as any).blog_category = formData.category;
      }

      // Make sure subType is set to 'blog' for proper filtering
      postData.subType = 'blog';

      // Ensure content is set
      if (!postData.content && postData.blogFullContent) {
        postData.content = postData.blogFullContent;
      }
    }

    // Special handling for event posts
    if (formData.postType === 'event') {
      // Ensure blog_category is set for database compatibility
      if (!postData.blog_category) {
        postData.blog_category = formData.eventType || formData.subType || 'general';
      }

      // Ensure event-specific fields are properly set
      if (!postData.event_type) {
        postData.event_type = formData.eventType || formData.subType || 'general';
      }

      if (!postData.event_start_datetime && formData.eventStartDatetime) {
        postData.event_start_datetime = formData.eventStartDatetime;
      }

      if (!postData.event_location && formData.eventLocation) {
        postData.event_location = formData.eventLocation;
      }

      // Make sure the title is set
      if (!postData.title) {
        postData.title = formData.eventTitle || formData.title || 'Event';
      }
    }

    console.log('Creating post with data:', postData);

    // Save the post to the database using the store
    const post = await postsStore.createPost(postData);

    if (post) {
      // Emit the created post to parent component
      emit('post-created', post);
      dialogOpen.value = false;
      // Show success notification
      notificationStore.success('Post created successfully!');
    } else {
      // Try again without the image if there was an error
      if (postData.image || postData.featuredImage) {
        console.log('Retrying post creation without image...');
        const postDataWithoutImage = {
          ...postData,
          image: null,
          featuredImage: null,
          images: []
        };

        const retryPost = await postsStore.createPost(postDataWithoutImage);

        if (retryPost) {
          emit('post-created', retryPost);
          dialogOpen.value = false;
          notificationStore.success('Post created successfully, but without the image.');
          return;
        }
      }

      throw new Error('Failed to create post. Please check all required fields and try again.');
    }
  } catch (error: any) {
    console.error('Error in handleFormSubmit:', error);
    // Show detailed error message
    if (error.message && error.message.includes('duplicate key')) {
      notificationStore.error('A post with this title already exists. Please use a different title.');
    } else if (error.message && error.message.includes('violates foreign key constraint')) {
      notificationStore.error('There was a database constraint error. Please try again or contact support.');
    } else if (error.message && error.message.includes('violates check constraint')) {
      notificationStore.error('One of the fields has an invalid value. Please check your inputs and try again.');
    } else {
      notificationStore.error(error.message || 'Failed to create post. Please try again.');
    }
  } finally {
    // Keep the loading state active for at least 1 second to show the loading animation
    // This ensures the user sees the loading state even if the operation is very fast
    console.log('Operation completed in parent, will set loading state to false after delay');
    setTimeout(() => {
      loading.value = false;
      console.log('Loading state set to false in parent after delay');
    }, 1000);
  }
}

// AI Trigger Handler
const handleAITrigger = async (triggerKey: string) => {
  try {
    await AIChatTriggerService.triggerChat(triggerKey);
  } catch (error) {
    console.error('Error triggering AI chat:', error);
  }
};

// Expose methods to the template ref
defineExpose({
  openDialog
});
</script>

<style scoped>
.post-creation-dialog {
  max-width: 800px;
  margin: 0 auto;
}

.auth-required {
  padding: 2rem;
}

.post-type-selector {
  max-width: 100%;
}

/* Style for the dropdown options */
.q-item__section--avatar .q-icon {
  font-size: 1.5rem;
}
</style>
